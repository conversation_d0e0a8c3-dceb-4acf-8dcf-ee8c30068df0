{"name": "zod", "version": "3.25.69", "type": "module", "author": "<PERSON> <<EMAIL>>", "description": "TypeScript-first schema declaration and validation library with static type inference", "files": ["**/*.js", "**/*.mjs", "**/*.cjs", "**/*.d.ts", "**/*.d.mts", "**/*.d.cts"], "funding": "https://github.com/sponsors/colinhacks", "homepage": "https://zod.dev", "keywords": ["typescript", "schema", "validation", "type", "inference"], "license": "MIT", "sideEffects": false, "main": "./index.cjs", "types": "./index.d.cts", "module": "./index.js", "zshy": {"exports": {"./package.json": "./package.json", ".": "./index.ts", "./v3": "./v3/index.ts", "./v4": "./v4/index.ts", "./v4-mini": "./v4-mini/index.ts", "./v4/mini": "./v4/mini/index.ts", "./v4/core": "./v4/core/index.ts", "./v4/locales": "./v4/locales/index.ts", "./v4/locales/*": "./v4/locales/*"}, "sourceDialects": ["@zod/source"]}, "exports": {"./package.json": "./package.json", ".": {"@zod/source": "./index.ts", "types": "./index.d.cts", "import": "./index.js", "require": "./index.cjs"}, "./v3": {"@zod/source": "./v3/index.ts", "types": "./v3/index.d.cts", "import": "./v3/index.js", "require": "./v3/index.cjs"}, "./v4": {"@zod/source": "./v4/index.ts", "types": "./v4/index.d.cts", "import": "./v4/index.js", "require": "./v4/index.cjs"}, "./v4-mini": {"@zod/source": "./v4-mini/index.ts", "types": "./v4-mini/index.d.cts", "import": "./v4-mini/index.js", "require": "./v4-mini/index.cjs"}, "./v4/mini": {"@zod/source": "./v4/mini/index.ts", "types": "./v4/mini/index.d.cts", "import": "./v4/mini/index.js", "require": "./v4/mini/index.cjs"}, "./v4/core": {"@zod/source": "./v4/core/index.ts", "types": "./v4/core/index.d.cts", "import": "./v4/core/index.js", "require": "./v4/core/index.cjs"}, "./v4/locales": {"@zod/source": "./v4/locales/index.ts", "types": "./v4/locales/index.d.cts", "import": "./v4/locales/index.js", "require": "./v4/locales/index.cjs"}, "./v4/locales/*": {"@zod/source": "./v4/locales/*", "types": "./v4/locales/*", "import": "./v4/locales/*", "require": "./v4/locales/*"}}, "repository": {"type": "git", "url": "git+https://github.com/colinhacks/zod.git"}, "bugs": {"url": "https://github.com/colinhacks/zod/issues"}, "support": {"backing": {"npm-funding": true}}, "scripts": {"clean": "rm -rf dist && git clean -qxdf . -e node_modules", "build": "pnpm clean && zshy", "postbuild": "pnpm biome check --write .", "test:watch": "pnpm vitest", "test": "pnpm vitest run", "test:resolution": "pnpm build && pnpm attw --pack . && pnpm run --filter @zod/resolution test", "bump:beta": "pnpm version \"v$(pnpm pkg get version | jq -r)-beta.$(date +%Y%m%dT%H%M%S)\"", "pub:beta": "pnpm bump:beta && pnpm publish --tag next --publish-branch v4 --no-git-checks --dry-run"}}