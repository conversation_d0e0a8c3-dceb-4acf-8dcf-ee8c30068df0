-- Debug Authentication Status
-- Run this in Supabase SQL Editor to check your auth status

-- Check all users in auth.users table
SELECT 
  id,
  email,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_user_meta_data,
  confirmation_token,
  email_change_token_current
FROM auth.users 
ORDER BY created_at DESC;

-- Check if user profile was created in public.users
SELECT 
  u.id,
  u.email,
  u.full_name,
  u.created_at as profile_created_at,
  au.email_confirmed_at,
  au.created_at as auth_created_at
FROM public.users u
RIGHT JOIN auth.users au ON u.id = au.id
ORDER BY au.created_at DESC;

-- Check for any pending confirmations
SELECT 
  id,
  email,
  confirmation_token IS NOT NULL as has_confirmation_token,
  email_confirmed_at IS NULL as needs_confirmation,
  created_at
FROM auth.users 
WHERE email_confirmed_at IS NULL
ORDER BY created_at DESC;
