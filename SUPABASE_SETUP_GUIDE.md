# CVLeap Supabase Setup Guide

## 🚀 Quick Setup Instructions

### Step 1: Database Schema Setup

1. **Open your Supabase Dashboard**
   - Go to https://supabase.com/dashboard
   - Select your project: `bxoockoddwlxoziutkfp`

2. **Run the Database Schema**
   - Navigate to **SQL Editor** in the left sidebar
   - Copy the entire content from `supabase-setup.sql`
   - Paste it into the SQL Editor
   - Click **Run** to execute the schema

3. **Add Seed Data (Optional)**
   - Copy the content from `supabase-seed-data.sql`
   - Paste it into the SQL Editor
   - Click **Run** to add sample data

### Step 2: Configure Email Authentication

1. **Enable Email Confirmation**
   - Go to **Authentication** → **Settings** in your Supabase dashboard
   - Under **User Signups**, ensure **Enable email confirmations** is checked
   - Set **Site URL** to: `http://localhost:3000`
   - Add **Redirect URLs**:
     - `http://localhost:3000/auth/verify-email`
     - `http://localhost:3000/dashboard`

2. **Configure Email Templates (Optional)**
   - Go to **Authentication** → **Email Templates**
   - Customize the **Confirm signup** template if needed
   - The default template should work fine for development

### Step 3: Test Email Verification

1. **Start the Development Server**
   ```bash
   npm run dev
   ```

2. **Test Signup Flow**
   - Go to http://localhost:3000/auth/signup
   - Create a new account
   - Check your email for verification link
   - Click the verification link

### Step 4: Configure Email Provider (Production)

For production, you'll need to configure a proper email provider:

1. **Go to Authentication → Settings → SMTP Settings**
2. **Choose an email provider:**
   - **Resend** (Recommended)
   - **SendGrid**
   - **Mailgun**
   - **Custom SMTP**

3. **For Resend (Recommended):**
   - Sign up at https://resend.com
   - Get your API key
   - In Supabase SMTP settings:
     - **Host**: `smtp.resend.com`
     - **Port**: `587`
     - **Username**: `resend`
     - **Password**: Your Resend API key

## 🔧 Environment Variables

Your `.env.local` should have:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://bxoockoddwlxoziutkfp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0WD4jtLt8-jvW2lJfA5M9IIyQVcfvQtCUGMTC8TyCBU

# Application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional: For production email
RESEND_API_KEY=your_resend_api_key
```

## 🗄️ Database Tables Created

The setup script creates these tables:

- **users** - User profiles and preferences
- **resume_templates** - Resume template definitions
- **resumes** - User resumes
- **companies** - Company information
- **jobs** - Job postings
- **job_applications** - Application tracking
- **job_search_loops** - Auto-apply configurations
- **email_templates** - Email templates
- **application_events** - Application event tracking
- **user_preferences** - User settings

## 🔐 Security Features

- **Row Level Security (RLS)** enabled on all user tables
- **Policies** ensure users can only access their own data
- **Triggers** for automatic timestamp updates
- **User creation trigger** for profile setup

## 🧪 Testing the Setup

1. **Create a test account**
2. **Verify email confirmation works**
3. **Check that user profile is created automatically**
4. **Test resume creation and job application tracking**

## 🚨 Troubleshooting

### Email Verification Not Working

1. **Check SMTP Settings** in Supabase Dashboard
2. **Verify Site URL** is set correctly
3. **Check spam folder** for verification emails
4. **Ensure email confirmations are enabled**

### Database Errors

1. **Check SQL execution** for any errors
2. **Verify all tables were created** in Database → Tables
3. **Check RLS policies** are active

### Authentication Issues

1. **Verify environment variables** are correct
2. **Check browser console** for errors
3. **Ensure Supabase project is active**

## 📧 Email Template Customization

You can customize email templates in Supabase:

1. Go to **Authentication** → **Email Templates**
2. Edit the **Confirm signup** template
3. Use these variables:
   - `{{ .ConfirmationURL }}` - Verification link
   - `{{ .SiteURL }}` - Your site URL
   - `{{ .Email }}` - User's email

## 🎯 Next Steps

After setup is complete:

1. **Test all authentication flows**
2. **Create sample resumes and applications**
3. **Configure production email provider**
4. **Set up monitoring and analytics**
5. **Deploy to production**

## 📞 Support

If you encounter issues:

1. Check the Supabase logs in Dashboard → Logs
2. Review the browser console for errors
3. Verify all environment variables are set
4. Ensure the database schema was applied correctly

---

**✅ Setup Complete!** Your CVLeap application should now have full email verification functionality.
