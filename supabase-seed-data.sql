-- C<PERSON><PERSON>eap Seed Data
-- Run this AFTER running supabase-setup.sql

-- Insert default resume templates
INSERT INTO public.resume_templates (id, name, description, category, is_premium, template_data) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  'Modern Professional',
  'A clean, modern template perfect for tech and business professionals',
  'modern',
  false,
  '{
    "layout": "two-column",
    "colors": {
      "primary": "#2563eb",
      "secondary": "#64748b",
      "accent": "#3b82f6",
      "text": "#1e293b",
      "background": "#ffffff"
    },
    "fonts": {
      "heading": "Inter",
      "body": "Inter"
    },
    "sections": [
      {"id": "personal", "name": "Personal Information", "order": 1, "required": true},
      {"id": "summary", "name": "Professional Summary", "order": 2, "required": false},
      {"id": "experience", "name": "Work Experience", "order": 3, "required": true},
      {"id": "education", "name": "Education", "order": 4, "required": true},
      {"id": "skills", "name": "Skills", "order": 5, "required": false},
      {"id": "projects", "name": "Projects", "order": 6, "required": false}
    ]
  }'
),
(
  '550e8400-e29b-41d4-a716-446655440002',
  'Classic Executive',
  'Traditional format ideal for senior executives and management roles',
  'classic',
  false,
  '{
    "layout": "single-column",
    "colors": {
      "primary": "#1f2937",
      "secondary": "#6b7280",
      "accent": "#374151",
      "text": "#111827",
      "background": "#ffffff"
    },
    "fonts": {
      "heading": "Georgia",
      "body": "Georgia"
    },
    "sections": [
      {"id": "personal", "name": "Contact Information", "order": 1, "required": true},
      {"id": "summary", "name": "Executive Summary", "order": 2, "required": true},
      {"id": "experience", "name": "Professional Experience", "order": 3, "required": true},
      {"id": "education", "name": "Education", "order": 4, "required": true},
      {"id": "skills", "name": "Core Competencies", "order": 5, "required": false},
      {"id": "certifications", "name": "Certifications", "order": 6, "required": false}
    ]
  }'
),
(
  '550e8400-e29b-41d4-a716-446655440003',
  'Creative Designer',
  'Bold and creative template for designers and creative professionals',
  'creative',
  true,
  '{
    "layout": "three-column",
    "colors": {
      "primary": "#7c3aed",
      "secondary": "#a855f7",
      "accent": "#c084fc",
      "text": "#1f2937",
      "background": "#ffffff"
    },
    "fonts": {
      "heading": "Poppins",
      "body": "Open Sans"
    },
    "sections": [
      {"id": "personal", "name": "Personal Info", "order": 1, "required": true},
      {"id": "summary", "name": "Creative Brief", "order": 2, "required": false},
      {"id": "experience", "name": "Experience", "order": 3, "required": true},
      {"id": "education", "name": "Education", "order": 4, "required": true},
      {"id": "skills", "name": "Skills", "order": 5, "required": false},
      {"id": "projects", "name": "Portfolio", "order": 6, "required": true}
    ]
  }'
),
(
  '550e8400-e29b-41d4-a716-446655440004',
  'Minimal Clean',
  'Ultra-clean minimal design for any profession',
  'minimal',
  false,
  '{
    "layout": "single-column",
    "colors": {
      "primary": "#000000",
      "secondary": "#666666",
      "accent": "#333333",
      "text": "#000000",
      "background": "#ffffff"
    },
    "fonts": {
      "heading": "Helvetica",
      "body": "Helvetica"
    },
    "sections": [
      {"id": "personal", "name": "Contact", "order": 1, "required": true},
      {"id": "experience", "name": "Experience", "order": 2, "required": true},
      {"id": "education", "name": "Education", "order": 3, "required": true},
      {"id": "skills", "name": "Skills", "order": 4, "required": false}
    ]
  }'
);

-- Insert sample companies
INSERT INTO public.companies (id, name, website, industry, size, location, description) VALUES
(
  '660e8400-e29b-41d4-a716-446655440001',
  'Google',
  'https://google.com',
  'Technology',
  '100,000+',
  'Mountain View, CA',
  'Multinational technology company specializing in Internet-related services and products'
),
(
  '660e8400-e29b-41d4-a716-446655440002',
  'Microsoft',
  'https://microsoft.com',
  'Technology',
  '100,000+',
  'Redmond, WA',
  'Multinational technology corporation that produces computer software, consumer electronics, and personal computers'
),
(
  '660e8400-e29b-41d4-a716-446655440003',
  'Apple',
  'https://apple.com',
  'Technology',
  '100,000+',
  'Cupertino, CA',
  'Multinational technology company that designs, develops, and sells consumer electronics and software'
),
(
  '660e8400-e29b-41d4-a716-446655440004',
  'Amazon',
  'https://amazon.com',
  'E-commerce/Cloud',
  '1,000,000+',
  'Seattle, WA',
  'Multinational technology company focusing on e-commerce, cloud computing, and artificial intelligence'
),
(
  '660e8400-e29b-41d4-a716-446655440005',
  'Meta',
  'https://meta.com',
  'Social Media',
  '50,000+',
  'Menlo Park, CA',
  'Technology company that builds social technology to help people connect, find communities, and grow businesses'
);

-- Insert sample jobs
INSERT INTO public.jobs (
  id, company_id, title, description, requirements, location, job_type, 
  experience_level, salary_min, salary_max, is_remote, skills, posted_date, 
  job_url, source, is_active
) VALUES
(
  '770e8400-e29b-41d4-a716-446655440001',
  '660e8400-e29b-41d4-a716-446655440001',
  'Senior Software Engineer',
  'Join our team to build next-generation web applications using modern technologies.',
  ARRAY['5+ years of software development experience', 'Proficiency in JavaScript/TypeScript', 'Experience with React or similar frameworks', 'Strong problem-solving skills'],
  'Mountain View, CA',
  'full-time',
  'senior',
  150000,
  200000,
  true,
  ARRAY['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python'],
  NOW() - INTERVAL '2 days',
  'https://careers.google.com/jobs/123',
  'company',
  true
),
(
  '770e8400-e29b-41d4-a716-446655440002',
  '660e8400-e29b-41d4-a716-446655440002',
  'Product Manager',
  'Lead product strategy and development for our cloud platform.',
  ARRAY['3+ years of product management experience', 'Experience with cloud technologies', 'Strong analytical skills', 'Excellent communication skills'],
  'Redmond, WA',
  'full-time',
  'mid',
  120000,
  160000,
  false,
  ARRAY['Product Management', 'Cloud Computing', 'Analytics', 'Strategy'],
  NOW() - INTERVAL '1 day',
  'https://careers.microsoft.com/jobs/456',
  'company',
  true
),
(
  '770e8400-e29b-41d4-a716-446655440003',
  '660e8400-e29b-41d4-a716-446655440003',
  'UX Designer',
  'Design intuitive user experiences for our mobile applications.',
  ARRAY['2+ years of UX design experience', 'Proficiency in Figma or Sketch', 'Understanding of mobile design principles', 'Portfolio of design work'],
  'Cupertino, CA',
  'full-time',
  'mid',
  100000,
  140000,
  true,
  ARRAY['UX Design', 'Figma', 'Mobile Design', 'Prototyping', 'Accessibility'],
  NOW() - INTERVAL '3 days',
  'https://jobs.apple.com/jobs/789',
  'company',
  true
);
