-- Fix Email Confirmation Issue
-- Run this ONLY if your email is confirmed but Supa<PERSON> still shows waiting

-- STEP 1: First, run debug-auth-status.sql to see the current state

-- STEP 2: If email_confirmed_at is NULL but you confirmed your email, 
-- replace '<EMAIL>' with your actual email and run this:

-- UPDATE auth.users 
-- SET 
--   email_confirmed_at = NOW(),
--   confirmation_token = NULL
-- WHERE email = '<EMAIL>' 
--   AND email_confirmed_at IS NULL;

-- STEP 3: Ensure user profile exists in public.users
-- Replace '<EMAIL>' with your actual email:

-- INSERT INTO public.users (id, email, full_name)
-- SELECT 
--   au.id, 
--   au.email, 
--   au.raw_user_meta_data->>'full_name'
-- FROM auth.users au
-- WHERE au.email = '<EMAIL>'
--   AND NOT EXISTS (
--     SELECT 1 FROM public.users pu WHERE pu.id = au.id
--   );

-- STEP 4: Alternative - Delete and recreate the user (ONLY if above doesn't work)
-- This will completely remove the user and allow you to sign up fresh
-- CAUTION: This will delete all user data!

-- DELETE FROM auth.users WHERE email = '<EMAIL>';

-- INSTRUCTIONS:
-- 1. First run debug-auth-status.sql to see what's wrong
-- 2. Uncomment and modify the appropriate fix above
-- 3. Replace '<EMAIL>' with your actual email
-- 4. Run the fix
-- 5. Try logging in again
