import { ResumeTemplate } from '@/types/resume';

export const defaultTemplates: ResumeTemplate[] = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    name: 'Modern Professional',
    description: 'A clean, modern template perfect for tech and business professionals',
    category: 'modern',
    isPremium: false,
    previewImage: '/templates/modern-professional.png',
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#3b82f6',
      text: '#1e293b',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Inter',
      body: 'Inter'
    },
    layout: 'two-column'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    name: 'Classic Executive',
    description: 'Traditional format ideal for senior executives and management roles',
    category: 'classic',
    isPremium: false,
    previewImage: '/templates/classic-executive.png',
    colors: {
      primary: '#1f2937',
      secondary: '#6b7280',
      accent: '#374151',
      text: '#111827',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Georgia',
      body: 'Georgia'
    },
    layout: 'single-column'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    name: 'Creative Designer',
    description: 'Bold and creative template for designers and creative professionals',
    category: 'creative',
    isPremium: true,
    previewImage: '/templates/creative-designer.png',
    colors: {
      primary: '#7c3aed',
      secondary: '#a855f7',
      accent: '#c084fc',
      text: '#1f2937',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Poppins',
      body: 'Open Sans'
    },
    layout: 'three-column'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440004',
    name: 'Minimal Clean',
    description: 'Ultra-clean minimal design for any profession',
    category: 'minimal',
    isPremium: false,
    previewImage: '/templates/minimal-clean.png',
    colors: {
      primary: '#000000',
      secondary: '#666666',
      accent: '#333333',
      text: '#000000',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Helvetica',
      body: 'Helvetica'
    },
    layout: 'single-column'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440005',
    name: 'Tech Innovator',
    description: 'Modern template designed for software engineers and tech professionals',
    category: 'modern',
    isPremium: true,
    previewImage: '/templates/tech-innovator.png',
    colors: {
      primary: '#059669',
      secondary: '#6b7280',
      accent: '#10b981',
      text: '#1f2937',
      background: '#ffffff'
    },
    fonts: {
      heading: 'JetBrains Mono',
      body: 'Inter'
    },
    layout: 'two-column'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440006',
    name: 'Corporate Elite',
    description: 'Professional template for corporate executives and business leaders',
    category: 'classic',
    isPremium: true,
    previewImage: '/templates/corporate-elite.png',
    colors: {
      primary: '#1e40af',
      secondary: '#64748b',
      accent: '#3b82f6',
      text: '#1e293b',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Times New Roman',
      body: 'Times New Roman'
    },
    layout: 'single-column'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440007',
    name: 'Artistic Flair',
    description: 'Vibrant and expressive template for artists and creative professionals',
    category: 'creative',
    isPremium: true,
    previewImage: '/templates/artistic-flair.png',
    colors: {
      primary: '#dc2626',
      secondary: '#f59e0b',
      accent: '#ef4444',
      text: '#1f2937',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Playfair Display',
      body: 'Source Sans Pro'
    },
    layout: 'three-column'
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440008',
    name: 'Simple Elegance',
    description: 'Elegant and understated design that works for any industry',
    category: 'minimal',
    isPremium: false,
    previewImage: '/templates/simple-elegance.png',
    colors: {
      primary: '#374151',
      secondary: '#9ca3af',
      accent: '#6b7280',
      text: '#1f2937',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Lato',
      body: 'Lato'
    },
    layout: 'single-column'
  }
];

export const getTemplateById = (id: string): ResumeTemplate | undefined => {
  return defaultTemplates.find(template => template.id === id);
};

export const getTemplatesByCategory = (category: string): ResumeTemplate[] => {
  if (category === 'all') return defaultTemplates;
  return defaultTemplates.filter(template => template.category === category);
};

export const getFreeTemplates = (): ResumeTemplate[] => {
  return defaultTemplates.filter(template => !template.isPremium);
};

export const getPremiumTemplates = (): ResumeTemplate[] => {
  return defaultTemplates.filter(template => template.isPremium);
};
