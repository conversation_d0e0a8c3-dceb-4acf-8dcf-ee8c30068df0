'use client';

import { useState } from 'react';
import { ResumeContent } from '@/types/resume';
import { Job } from '@/types/job';
import { ResumeOptimizer, OptimizationSuggestion } from '@/lib/ai/resume-optimizer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Target, 
  Search, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  ExternalLink,
  Zap
} from 'lucide-react';
import toast from 'react-hot-toast';

interface JobMatchOptimizerProps {
  resume: ResumeContent;
  onOptimize?: (suggestions: OptimizationSuggestion[]) => void;
}

export function JobMatchOptimizer({ resume, onOptimize }: JobMatchOptimizerProps) {
  const [jobUrl, setJobUrl] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [matchScore, setMatchScore] = useState<number | null>(null);

  const optimizer = new ResumeOptimizer();

  // Mock job data for demonstration
  const mockJob: Job = {
    id: 'demo-job',
    title: 'Senior Software Engineer',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    type: 'full-time',
    description: 'We are looking for a Senior Software Engineer to join our team. You will work with React, Node.js, and AWS to build scalable applications.',
    requirements: [
      '5+ years of software development experience',
      'Proficiency in JavaScript and TypeScript',
      'Experience with React and Node.js',
      'Knowledge of AWS cloud services',
      'Strong problem-solving skills'
    ],
    skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'AWS', 'Docker', 'PostgreSQL'],
    experienceLevel: 'senior',
    isRemote: true,
    postedDate: new Date().toISOString(),
    url: 'https://example.com/job',
    source: 'company',
    benefits: [],
    companyInfo: {
      industry: 'Technology',
      size: '100-500',
      website: 'https://techcorp.com'
    }
  };

  const analyzeJobMatch = async () => {
    if (!jobUrl.trim()) {
      toast.error('Please enter a job URL');
      return;
    }

    setIsAnalyzing(true);
    try {
      // In a real implementation, this would parse the job URL and extract job details
      // For demo purposes, we'll use the mock job
      const jobSuggestions = await optimizer.optimizeForJob(resume, mockJob);
      setSuggestions(jobSuggestions);
      
      // Calculate match score based on suggestions
      const score = Math.max(0, 100 - (jobSuggestions.length * 10));
      setMatchScore(score);
      
      toast.success(`Analysis complete! Found ${jobSuggestions.length} optimization opportunities`);
    } catch (error) {
      console.error('Failed to analyze job match:', error);
      toast.error('Failed to analyze job match');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMatchScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent Match';
    if (score >= 60) return 'Good Match';
    if (score >= 40) return 'Fair Match';
    return 'Poor Match';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertCircle className="w-4 h-4" />;
      case 'medium': return <TrendingUp className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Target className="w-5 h-5 text-purple-600" />
          <span>Job Match Optimizer</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Job URL Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Job Posting URL
          </label>
          <div className="flex space-x-2">
            <Input
              placeholder="Paste job posting URL here..."
              value={jobUrl}
              onChange={(e) => setJobUrl(e.target.value)}
              disabled={isAnalyzing}
            />
            <Button 
              onClick={analyzeJobMatch}
              disabled={isAnalyzing || !jobUrl.trim()}
            >
              {isAnalyzing ? (
                <>
                  <Search className="w-4 h-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Search className="w-4 h-4 mr-2" />
                  Analyze
                </>
              )}
            </Button>
          </div>
          <p className="text-xs text-gray-500">
            Enter a job posting URL to get personalized optimization suggestions
          </p>
        </div>

        {/* Match Score */}
        {matchScore !== null && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Job Match Score</span>
              <div className={`text-2xl font-bold ${getMatchScoreColor(matchScore)}`}>
                {matchScore}%
              </div>
            </div>
            <p className={`text-sm ${getMatchScoreColor(matchScore)}`}>
              {getMatchScoreLabel(matchScore)}
            </p>
          </div>
        )}

        {/* Optimization Suggestions */}
        {suggestions.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm text-gray-700">
                Optimization Suggestions ({suggestions.length})
              </h4>
              {onOptimize && (
                <Button size="sm" onClick={() => onOptimize(suggestions)}>
                  <Zap className="w-4 h-4 mr-1" />
                  Apply All
                </Button>
              )}
            </div>

            <div className="space-y-3">
              {suggestions.map((suggestion) => (
                <div key={suggestion.id} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getSeverityIcon(suggestion.severity)}
                      <span className="font-medium text-sm">{suggestion.title}</span>
                    </div>
                    <Badge className={getSeverityColor(suggestion.severity)}>
                      {suggestion.severity}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    {suggestion.description}
                  </p>
                  
                  <div className="bg-blue-50 p-3 rounded-md">
                    <p className="text-sm text-blue-800 font-medium">
                      💡 {suggestion.suggestion}
                    </p>
                  </div>

                  {suggestion.currentValue && suggestion.suggestedValue && (
                    <div className="mt-3 space-y-2">
                      <div className="text-xs">
                        <span className="font-medium text-gray-700">Current:</span>
                        <p className="text-gray-600 bg-gray-50 p-2 rounded mt-1">
                          {suggestion.currentValue}
                        </p>
                      </div>
                      <div className="text-xs">
                        <span className="font-medium text-gray-700">Suggested:</span>
                        <p className="text-green-700 bg-green-50 p-2 rounded mt-1">
                          {suggestion.suggestedValue}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Demo Notice */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <ExternalLink className="w-4 h-4 text-blue-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-800">Demo Mode</p>
              <p className="text-blue-700">
                This feature analyzes job postings to provide personalized optimization suggestions. 
                In the full version, it would parse real job URLs and provide specific recommendations.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
