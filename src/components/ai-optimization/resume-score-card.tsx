'use client';

import { useState, useEffect } from 'react';
import { ResumeContent } from '@/types/resume';
import { ResumeOptimizer, ResumeScore } from '@/lib/ai/resume-optimizer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle, 
  Lightbulb,
  RefreshCw,
  Zap
} from 'lucide-react';

interface ResumeScoreCardProps {
  resume: ResumeContent;
  onOptimize?: (suggestions: any[]) => void;
}

export function ResumeScoreCard({ resume, onOptimize }: ResumeScoreCardProps) {
  const [score, setScore] = useState<ResumeScore | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const optimizer = new ResumeOptimizer();

  useEffect(() => {
    analyzeResume();
  }, [resume]);

  const analyzeResume = async () => {
    setIsAnalyzing(true);
    try {
      const analysis = await optimizer.analyzeResume(resume);
      setScore(analysis);
    } catch (error) {
      console.error('Failed to analyze resume:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Improvement';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertCircle className="w-4 h-4" />;
      case 'medium': return <Lightbulb className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  if (isAnalyzing) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <RefreshCw className="w-5 h-5 animate-spin text-blue-600" />
            <span>Analyzing your resume...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!score) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">Get AI-powered insights about your resume</p>
            <Button onClick={analyzeResume}>
              <Zap className="w-4 h-4 mr-2" />
              Analyze Resume
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="w-5 h-5 text-blue-600" />
            <span>Resume Score</span>
          </div>
          <Button variant="ghost" size="sm" onClick={analyzeResume}>
            <RefreshCw className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Score */}
        <div className="text-center">
          <div className={`text-4xl font-bold ${getScoreColor(score.overall)}`}>
            {score.overall}%
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {getScoreLabel(score.overall)}
          </p>
          <Progress value={score.overall} className="mt-3" />
        </div>

        {/* Section Scores */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-gray-700">Section Breakdown</h4>
          {Object.entries(score.sections).map(([section, sectionScore]) => (
            <div key={section} className="flex items-center justify-between">
              <span className="text-sm capitalize">
                {section === 'personalInfo' ? 'Personal Info' : section}
              </span>
              <div className="flex items-center space-x-2">
                <div className="w-20">
                  <Progress value={sectionScore} className="h-2" />
                </div>
                <span className={`text-sm font-medium ${getScoreColor(sectionScore)}`}>
                  {sectionScore}%
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Top Improvements */}
        {score.improvements.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm text-gray-700">
                Top Improvements ({score.improvements.length})
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
              >
                {showDetails ? 'Hide' : 'Show'} Details
              </Button>
            </div>

            <div className="space-y-2">
              {(showDetails ? score.improvements : score.improvements.slice(0, 3)).map((improvement) => (
                <div key={improvement.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getSeverityIcon(improvement.severity)}
                      <span className="font-medium text-sm">{improvement.title}</span>
                    </div>
                    <Badge className={getSeverityColor(improvement.severity)}>
                      {improvement.severity}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{improvement.description}</p>
                  <p className="text-xs text-blue-600 font-medium">{improvement.suggestion}</p>
                </div>
              ))}
            </div>

            {!showDetails && score.improvements.length > 3 && (
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => setShowDetails(true)}
              >
                View {score.improvements.length - 3} More Suggestions
              </Button>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-2 pt-4 border-t">
          {onOptimize && (
            <Button 
              className="w-full" 
              onClick={() => onOptimize(score.improvements)}
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              Apply AI Suggestions
            </Button>
          )}
          
          <Button variant="outline" className="w-full">
            <Lightbulb className="w-4 h-4 mr-2" />
            Get Personalized Tips
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
