'use client';

import { Job } from '@/types/job';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  MapPin, 
  DollarSign, 
  Clock, 
  Building, 
  Users, 
  Globe,
  Bookmark,
  BookmarkCheck,
  ExternalLink,
  Send
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface JobDetailsProps {
  job: Job;
  isSaved: boolean;
  onToggleSave: () => void;
  onClose: () => void;
}

export function JobDetails({ job, isSaved, onToggleSave, onClose }: JobDetailsProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours} hours ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} days ago`;
    }
  };

  const formatSalary = (job: Job) => {
    if (!job.salary) return 'Salary not specified';
    
    const { min, max, currency, period } = job.salary;
    const periodText = period === 'hourly' ? 'per hour' : period === 'monthly' ? 'per month' : 'per year';
    
    if (min && max) {
      return `${formatCurrency(min, currency)} - ${formatCurrency(max, currency)} ${periodText}`;
    } else if (min) {
      return `${formatCurrency(min, currency)}+ ${periodText}`;
    } else if (max) {
      return `Up to ${formatCurrency(max, currency)} ${periodText}`;
    }
    
    return 'Salary not specified';
  };

  const handleApply = () => {
    // TODO: Implement job application logic
    window.open(job.url, '_blank');
  };

  return (
    <Card className="sticky top-24 max-h-[calc(100vh-8rem)] overflow-auto">
      <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-4">
        <div className="flex-1">
          <CardTitle className="text-xl mb-2">{job.title}</CardTitle>
          <div className="flex items-center text-gray-600 mb-2">
            <Building className="w-4 h-4 mr-2" />
            <span className="font-medium">{job.company}</span>
          </div>
          <div className="flex items-center text-gray-600">
            <MapPin className="w-4 h-4 mr-2" />
            <span>{job.location}</span>
            {job.isRemote && (
              <Badge variant="secondary" className="ml-2 text-xs">
                Remote
              </Badge>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={onToggleSave}>
            {isSaved ? (
              <BookmarkCheck className="w-4 h-4 text-blue-600" />
            ) : (
              <Bookmark className="w-4 h-4" />
            )}
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Quick Info */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="space-y-2">
            <div className="flex items-center text-sm text-gray-600">
              <DollarSign className="w-4 h-4 mr-2" />
              <span>{formatSalary(job)}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="w-4 h-4 mr-2" />
              <span>Posted {formatTimeAgo(job.postedDate)}</span>
            </div>
          </div>
          <div className="space-y-2">
            <Badge variant="outline" className="text-xs">
              {job.type.charAt(0).toUpperCase() + job.type.slice(1)}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {job.experienceLevel.charAt(0).toUpperCase() + job.experienceLevel.slice(1)} Level
            </Badge>
          </div>
        </div>

        {/* Apply Button */}
        <div className="space-y-2">
          <Button onClick={handleApply} className="w-full">
            <Send className="w-4 h-4 mr-2" />
            Apply Now
          </Button>
          <Button variant="outline" className="w-full">
            <ExternalLink className="w-4 h-4 mr-2" />
            View on {job.source.charAt(0).toUpperCase() + job.source.slice(1)}
          </Button>
        </div>

        {/* Company Info */}
        {job.companyInfo && (
          <div className="space-y-3">
            <h3 className="font-semibold">About {job.company}</h3>
            <div className="flex items-start space-x-3">
              {job.companyInfo.logo && (
                <img
                  src={job.companyInfo.logo}
                  alt={`${job.company} logo`}
                  className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                />
              )}
              <div className="space-y-2 text-sm">
                {job.companyInfo.industry && (
                  <div className="flex items-center text-gray-600">
                    <Building className="w-4 h-4 mr-2" />
                    <span>{job.companyInfo.industry}</span>
                  </div>
                )}
                {job.companyInfo.size && (
                  <div className="flex items-center text-gray-600">
                    <Users className="w-4 h-4 mr-2" />
                    <span>{job.companyInfo.size} employees</span>
                  </div>
                )}
                {job.companyInfo.website && (
                  <div className="flex items-center text-gray-600">
                    <Globe className="w-4 h-4 mr-2" />
                    <a 
                      href={job.companyInfo.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      Company Website
                    </a>
                  </div>
                )}
              </div>
            </div>
            {job.companyInfo.description && (
              <p className="text-sm text-gray-700">{job.companyInfo.description}</p>
            )}
          </div>
        )}

        {/* Job Description */}
        <div className="space-y-3">
          <h3 className="font-semibold">Job Description</h3>
          <div className="text-sm text-gray-700 whitespace-pre-line">
            {job.description}
          </div>
        </div>

        {/* Requirements */}
        {job.requirements.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Requirements</h3>
            <ul className="space-y-2">
              {job.requirements.map((requirement, index) => (
                <li key={index} className="flex items-start text-sm text-gray-700">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  {requirement}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Benefits */}
        {job.benefits && job.benefits.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Benefits</h3>
            <ul className="space-y-2">
              {job.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start text-sm text-gray-700">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  {benefit}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Skills */}
        {job.skills.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Required Skills</h3>
            <div className="flex flex-wrap gap-2">
              {job.skills.map((skill, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
