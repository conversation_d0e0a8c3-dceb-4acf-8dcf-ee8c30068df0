'use client';

import { useState } from 'react';
import { JobSearchFilters as JobFilters } from '@/types/job';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';

interface JobSearchFiltersProps {
  filters: JobFilters;
  onFiltersChange: (filters: JobFilters) => void;
  onApply: () => void;
  onClose: () => void;
}

export function JobSearchFilters({
  filters,
  onFiltersChange,
  onApply,
  onClose,
}: JobSearchFiltersProps) {
  const [newKeyword, setNewKeyword] = useState('');

  const jobTypes = [
    { value: 'full-time', label: 'Full-time' },
    { value: 'part-time', label: 'Part-time' },
    { value: 'contract', label: 'Contract' },
    { value: 'internship', label: 'Internship' },
    { value: 'remote', label: 'Remote' },
  ];

  const experienceLevels = [
    { value: 'entry', label: 'Entry Level' },
    { value: 'mid', label: 'Mid Level' },
    { value: 'senior', label: 'Senior Level' },
    { value: 'executive', label: 'Executive' },
  ];

  const datePostedOptions = [
    { value: 'any', label: 'Any time' },
    { value: '1day', label: 'Past 24 hours' },
    { value: '3days', label: 'Past 3 days' },
    { value: '7days', label: 'Past week' },
    { value: '14days', label: 'Past 2 weeks' },
    { value: '30days', label: 'Past month' },
  ];

  const updateFilters = (updates: Partial<JobFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !filters.keywords.includes(newKeyword.trim())) {
      updateFilters({
        keywords: [...filters.keywords, newKeyword.trim()]
      });
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    updateFilters({
      keywords: filters.keywords.filter(k => k !== keyword)
    });
  };

  const toggleJobType = (jobType: string) => {
    const newJobTypes = filters.jobType.includes(jobType)
      ? filters.jobType.filter(t => t !== jobType)
      : [...filters.jobType, jobType];
    updateFilters({ jobType: newJobTypes });
  };

  const toggleExperienceLevel = (level: string) => {
    const newLevels = filters.experienceLevel.includes(level)
      ? filters.experienceLevel.filter(l => l !== level)
      : [...filters.experienceLevel, level];
    updateFilters({ experienceLevel: newLevels });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      keywords: [],
      location: '',
      radius: 25,
      jobType: [],
      experienceLevel: [],
      salaryMin: undefined,
      salaryMax: undefined,
      datePosted: 'any',
      remote: false,
      companies: [],
      excludeCompanies: [],
    });
  };

  return (
    <Card className="sticky top-24">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">Filters</CardTitle>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Keywords */}
        <div className="space-y-2">
          <Label>Keywords</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Add keyword"
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
            />
            <Button size="sm" onClick={addKeyword}>
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          {filters.keywords.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {filters.keywords.map((keyword) => (
                <Badge key={keyword} variant="secondary" className="text-xs">
                  {keyword}
                  <button
                    onClick={() => removeKeyword(keyword)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Job Type */}
        <div className="space-y-2">
          <Label>Job Type</Label>
          <div className="space-y-2">
            {jobTypes.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`jobType-${type.value}`}
                  checked={filters.jobType.includes(type.value)}
                  onChange={() => toggleJobType(type.value)}
                  className="rounded"
                />
                <Label htmlFor={`jobType-${type.value}`} className="text-sm">
                  {type.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Experience Level */}
        <div className="space-y-2">
          <Label>Experience Level</Label>
          <div className="space-y-2">
            {experienceLevels.map((level) => (
              <div key={level.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`experience-${level.value}`}
                  checked={filters.experienceLevel.includes(level.value)}
                  onChange={() => toggleExperienceLevel(level.value)}
                  className="rounded"
                />
                <Label htmlFor={`experience-${level.value}`} className="text-sm">
                  {level.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Salary Range */}
        <div className="space-y-2">
          <Label>Salary Range (USD)</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Input
                type="number"
                placeholder="Min"
                value={filters.salaryMin || ''}
                onChange={(e) => updateFilters({ 
                  salaryMin: e.target.value ? parseInt(e.target.value) : undefined 
                })}
              />
            </div>
            <div>
              <Input
                type="number"
                placeholder="Max"
                value={filters.salaryMax || ''}
                onChange={(e) => updateFilters({ 
                  salaryMax: e.target.value ? parseInt(e.target.value) : undefined 
                })}
              />
            </div>
          </div>
        </div>

        {/* Date Posted */}
        <div className="space-y-2">
          <Label>Date Posted</Label>
          <select
            value={filters.datePosted}
            onChange={(e) => updateFilters({ datePosted: e.target.value as any })}
            className="w-full p-2 border border-gray-300 rounded-md text-sm"
          >
            {datePostedOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Remote Work */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="remote"
            checked={filters.remote}
            onChange={(e) => updateFilters({ remote: e.target.checked })}
            className="rounded"
          />
          <Label htmlFor="remote" className="text-sm">
            Remote work only
          </Label>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2 pt-4 border-t">
          <Button onClick={onApply} className="w-full">
            Apply Filters
          </Button>
          <Button variant="outline" onClick={clearAllFilters} className="w-full">
            Clear All
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
