'use client';

import { useState } from 'react';
import { JobApplication, ApplicationStatus } from '@/types/job';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Building, 
  Calendar, 
  ExternalLink, 
  MessageSquare,
  Edit3,
  Check,
  X,
  MoreVertical,
  Clock
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface ApplicationsListProps {
  applications: JobApplication[];
  onStatusUpdate: (applicationId: string, status: ApplicationStatus) => void;
  onAddNote: (applicationId: string, note: string) => void;
  isLoading: boolean;
}

export function ApplicationsList({
  applications,
  onStatusUpdate,
  onAddNote,
  isLoading,
}: ApplicationsListProps) {
  const [editingNote, setEditingNote] = useState<string | null>(null);
  const [noteText, setNoteText] = useState('');

  const getStatusColor = (status: ApplicationStatus) => {
    const colors = {
      applied: 'bg-blue-100 text-blue-800',
      viewed: 'bg-purple-100 text-purple-800',
      screening: 'bg-yellow-100 text-yellow-800',
      interview: 'bg-orange-100 text-orange-800',
      offer: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      withdrawn: 'bg-gray-100 text-gray-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (status: ApplicationStatus) => {
    const texts = {
      applied: 'Applied',
      viewed: 'Viewed',
      screening: 'Screening',
      interview: 'Interview',
      offer: 'Offer',
      rejected: 'Rejected',
      withdrawn: 'Withdrawn',
    };
    return texts[status] || status;
  };

  const handleEditNote = (application: JobApplication) => {
    setEditingNote(application.id);
    setNoteText(application.notes || '');
  };

  const handleSaveNote = (applicationId: string) => {
    onAddNote(applicationId, noteText);
    setEditingNote(null);
    setNoteText('');
  };

  const handleCancelEdit = () => {
    setEditingNote(null);
    setNoteText('');
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (applications.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No applications found</h3>
          <p className="text-gray-600">
            Start applying to jobs to track your applications here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {applications.map((application) => (
        <Card key={application.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {application.job_title}
                    </h3>
                    <div className="flex items-center text-gray-600 mt-1">
                      <Building className="w-4 h-4 mr-1" />
                      <span>{application.company_name}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(application.status)}>
                      {getStatusText(application.status)}
                    </Badge>
                    
                    <select
                      value={application.status}
                      onChange={(e) => onStatusUpdate(application.id, e.target.value as ApplicationStatus)}
                      className="text-xs border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="applied">Applied</option>
                      <option value="viewed">Viewed</option>
                      <option value="screening">Screening</option>
                      <option value="interview">Interview</option>
                      <option value="offer">Offer</option>
                      <option value="rejected">Rejected</option>
                      <option value="withdrawn">Withdrawn</option>
                    </select>
                  </div>
                </div>

                {/* Application Details */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>Applied {formatTimeAgo(application.applied_at)}</span>
                  </div>
                  
                  {application.response_at && (
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      <span>Response {formatTimeAgo(application.response_at)}</span>
                    </div>
                  )}
                  
                  {application.interview_date && (
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>Interview {formatDate(application.interview_date)}</span>
                    </div>
                  )}
                  
                  {application.job_url && (
                    <a
                      href={application.job_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-600 hover:text-blue-800"
                    >
                      <ExternalLink className="w-4 h-4 mr-1" />
                      <span>View Job</span>
                    </a>
                  )}
                </div>

                {/* Notes Section */}
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-700">Notes</h4>
                    {editingNote !== application.id && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditNote(application)}
                      >
                        <Edit3 className="w-4 h-4 mr-1" />
                        {application.notes ? 'Edit' : 'Add Note'}
                      </Button>
                    )}
                  </div>

                  {editingNote === application.id ? (
                    <div className="space-y-2">
                      <Textarea
                        value={noteText}
                        onChange={(e) => setNoteText(e.target.value)}
                        placeholder="Add notes about this application..."
                        rows={3}
                      />
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          onClick={() => handleSaveNote(application.id)}
                        >
                          <Check className="w-4 h-4 mr-1" />
                          Save
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCancelEdit}
                        >
                          <X className="w-4 h-4 mr-1" />
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-600">
                      {application.notes ? (
                        <p className="whitespace-pre-wrap">{application.notes}</p>
                      ) : (
                        <p className="italic">No notes added yet</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
