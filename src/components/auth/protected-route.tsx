'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from './simple-auth-provider';
import { supabase } from '@/lib/supabase/client';
import { FileText } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export function ProtectedRoute({ 
  children, 
  redirectTo = '/auth/signin' 
}: ProtectedRouteProps) {
  const { user, loading } = useSimpleAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      console.log('ProtectedRoute: No user found, checking direct auth...');

      // Check direct auth before redirecting
      const checkDirectAuth = async () => {
        try {
          const { data: { user: directUser } } = await supabase.auth.getUser();
          if (!directUser) {
            console.log('ProtectedRoute: No direct user found, redirecting to:', redirectTo);
            router.push(redirectTo);
          } else {
            console.log('ProtectedRoute: Direct user found, staying on page');
            // User exists but provider hasn't updated yet, wait a bit
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        } catch (error) {
          console.error('ProtectedRoute: Auth check failed:', error);
          router.push(redirectTo);
        }
      };

      checkDirectAuth();
    }
  }, [user, loading, router, redirectTo]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center animate-pulse">
              <FileText className="w-7 h-7 text-white" />
            </div>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Loading...
          </h1>
          <p className="text-gray-600">
            Please wait while we load your account.
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return <>{children}</>;
}
