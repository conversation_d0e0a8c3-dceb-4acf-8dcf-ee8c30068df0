'use client';

import { JobSearchLoop } from '@/types/job';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  Settings, 
  MoreVertical,
  Clock,
  Target,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface SearchLoopsListProps {
  searchLoops: JobSearchLoop[];
  onToggleLoop: (loopId: string, isActive: boolean) => void;
  onRunLoop: (loop: JobSearchLoop) => void;
}

export function SearchLoopsList({
  searchLoops,
  onToggleLoop,
  onRunLoop,
}: SearchLoopsListProps) {
  const getStatusColor = (loop: JobSearchLoop) => {
    if (!loop.is_active) return 'bg-gray-100 text-gray-800';
    if (loop.auto_apply) return 'bg-green-100 text-green-800';
    return 'bg-blue-100 text-blue-800';
  };

  const getStatusText = (loop: JobSearchLoop) => {
    if (!loop.is_active) return 'Paused';
    if (loop.auto_apply) return 'Auto-applying';
    return 'Active';
  };

  const formatFilters = (filters: any) => {
    const filterTexts = [];
    
    if (filters.keywords?.length > 0) {
      filterTexts.push(`Keywords: ${filters.keywords.join(', ')}`);
    }
    
    if (filters.location) {
      filterTexts.push(`Location: ${filters.location}`);
    }
    
    if (filters.jobType?.length > 0) {
      filterTexts.push(`Type: ${filters.jobType.join(', ')}`);
    }
    
    if (filters.experienceLevel?.length > 0) {
      filterTexts.push(`Level: ${filters.experienceLevel.join(', ')}`);
    }
    
    if (filters.remote) {
      filterTexts.push('Remote only');
    }
    
    return filterTexts.length > 0 ? filterTexts.join(' • ') : 'No specific filters';
  };

  return (
    <div className="space-y-4">
      {searchLoops.map((loop) => (
        <Card key={loop.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CardTitle className="text-lg">{loop.name}</CardTitle>
                <Badge className={getStatusColor(loop)}>
                  {getStatusText(loop)}
                </Badge>
                {loop.auto_apply && (
                  <Badge variant="outline" className="text-xs">
                    Auto-Apply Enabled
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onRunLoop(loop)}
                  disabled={!loop.is_active}
                >
                  <Play className="w-4 h-4 mr-1" />
                  Run Now
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onToggleLoop(loop.id, !loop.is_active)}
                >
                  {loop.is_active ? (
                    <>
                      <Pause className="w-4 h-4 mr-1" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-1" />
                      Activate
                    </>
                  )}
                </Button>
                
                <Button variant="ghost" size="sm">
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Search Filters */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Search Criteria</h4>
              <p className="text-sm text-gray-600">
                {formatFilters(loop.search_filters)}
              </p>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Target className="w-4 h-4 text-blue-600 mr-1" />
                  <span className="text-sm font-medium text-gray-700">Total</span>
                </div>
                <div className="text-lg font-bold text-gray-900">
                  {loop.total_applications}
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <CheckCircle className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-sm font-medium text-gray-700">Success</span>
                </div>
                <div className="text-lg font-bold text-gray-900">
                  {loop.successful_applications}
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Clock className="w-4 h-4 text-purple-600 mr-1" />
                  <span className="text-sm font-medium text-gray-700">Last Run</span>
                </div>
                <div className="text-sm text-gray-900">
                  {loop.last_run_at 
                    ? formatDate(loop.last_run_at)
                    : 'Never'
                  }
                </div>
              </div>
            </div>

            {/* Success Rate */}
            {loop.total_applications > 0 && (
              <div>
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-gray-700">Success Rate</span>
                  <span className="font-medium">
                    {((loop.successful_applications / loop.total_applications) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full transition-all"
                    style={{ 
                      width: `${(loop.successful_applications / loop.total_applications) * 100}%` 
                    }}
                  />
                </div>
              </div>
            )}

            {/* Email Template Preview */}
            {loop.email_template && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Cover Letter Template</h4>
                <div className="bg-gray-50 rounded-lg p-3">
                  <p className="text-sm text-gray-600 line-clamp-3">
                    {loop.email_template}
                  </p>
                </div>
              </div>
            )}

            {/* Warnings */}
            {!loop.is_active && (
              <div className="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <AlertCircle className="w-4 h-4 text-yellow-600 mr-2 flex-shrink-0" />
                <span className="text-sm text-yellow-800">
                  This search loop is paused and won't automatically apply to jobs.
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
