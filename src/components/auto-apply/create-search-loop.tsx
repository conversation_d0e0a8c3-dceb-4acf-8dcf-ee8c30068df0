'use client';

import { useState } from 'react';
import { JobSearchLoop } from '@/types/job';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { X } from 'lucide-react';

interface CreateSearchLoopProps {
  onClose: () => void;
  onCreate: (loop: Partial<JobSearchLoop>) => void;
}

export function CreateSearchLoop({ onClose, onCreate }: CreateSearchLoopProps) {
  const [formData, setFormData] = useState({
    name: '',
    keywords: '',
    location: '',
    autoApply: false,
    emailTemplate: `Dear Hiring Manager,

I am writing to express my interest in the {position} position at {company}. With my experience in {skills}, I believe I would be a valuable addition to your team.

I am particularly drawn to this opportunity because of {company}'s reputation in the industry. I am excited about the possibility of contributing to your team and would welcome the opportunity to discuss how my skills and experience align with your needs.

Thank you for considering my application. I look forward to hearing from you.

Best regards,
[Your Name]`
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const searchFilters = {
      keywords: formData.keywords.split(',').map(k => k.trim()).filter(k => k),
      location: formData.location,
      radius: 25,
      jobType: [],
      experienceLevel: [],
      datePosted: 'any' as const,
      remote: false,
      companies: [],
      excludeCompanies: [],
    };

    onCreate({
      name: formData.name,
      search_filters: searchFilters,
      is_active: false,
      auto_apply: formData.autoApply,
      email_template: formData.emailTemplate,
      total_applications: 0,
      successful_applications: 0,
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Create Search Loop</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Loop Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Frontend Developer Jobs"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="keywords">Keywords</Label>
              <Input
                id="keywords"
                value={formData.keywords}
                onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
                placeholder="e.g., React, JavaScript, Frontend (comma-separated)"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="e.g., San Francisco, CA"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoApply"
                checked={formData.autoApply}
                onChange={(e) => setFormData({ ...formData, autoApply: e.target.checked })}
                className="rounded"
              />
              <Label htmlFor="autoApply">
                Enable automatic job applications
              </Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="emailTemplate">Cover Letter Template</Label>
              <Textarea
                id="emailTemplate"
                value={formData.emailTemplate}
                onChange={(e) => setFormData({ ...formData, emailTemplate: e.target.value })}
                rows={8}
                placeholder="Your cover letter template..."
              />
              <p className="text-sm text-gray-500">
                Use placeholders: {'{company}'}, {'{position}'}, {'{skills}'} for personalization
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">
                Create Search Loop
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
