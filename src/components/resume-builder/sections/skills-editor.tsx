'use client';

import { Skill } from '@/types/resume';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface SkillsEditorProps {
  data: Skill[];
  onChange: (data: Skill[]) => void;
}

export function SkillsEditor({ data, onChange }: SkillsEditorProps) {
  return (
    <div className="space-y-4">
      <div className="text-center py-8 text-gray-500">
        <p>Skills editor coming soon!</p>
        <Button variant="outline" className="mt-4">
          <Plus className="w-4 h-4 mr-2" />
          Add Skill
        </Button>
      </div>
    </div>
  );
}
