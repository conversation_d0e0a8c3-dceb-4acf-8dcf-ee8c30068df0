'use client';

import { Project } from '@/types/resume';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface ProjectsEditorProps {
  data: Project[];
  onChange: (data: Project[]) => void;
}

export function ProjectsEditor({ data, onChange }: ProjectsEditorProps) {
  return (
    <div className="space-y-4">
      <div className="text-center py-8 text-gray-500">
        <p>Projects editor coming soon!</p>
        <Button variant="outline" className="mt-4">
          <Plus className="w-4 h-4 mr-2" />
          Add Project
        </Button>
      </div>
    </div>
  );
}
