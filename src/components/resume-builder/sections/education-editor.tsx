'use client';

import { Education } from '@/types/resume';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface EducationEditorProps {
  data: Education[];
  onChange: (data: Education[]) => void;
}

export function EducationEditor({ data, onChange }: EducationEditorProps) {
  return (
    <div className="space-y-4">
      <div className="text-center py-8 text-gray-500">
        <p>Education editor coming soon!</p>
        <Button variant="outline" className="mt-4">
          <Plus className="w-4 h-4 mr-2" />
          Add Education
        </Button>
      </div>
    </div>
  );
}
