'use client';

import { ResumeContent, ResumeTemplate } from '@/types/resume';
import { formatDate } from '@/lib/utils';
import { ModernTemplate } from './templates/modern-template';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

interface ResumePreviewProps {
  content: ResumeContent;
  template: ResumeTemplate | null;
}

export function ResumePreview({ content, template }: ResumePreviewProps) {
  if (!template) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <p>Select a template to preview your resume</p>
      </div>
    );
  }

  // Use different template renderers based on layout
  if (template.layout === 'two-column' && template.category === 'modern') {
    return <ModernTemplate content={content} template={template} />;
  }

  // Fallback to default template
  const { personalInfo, experience, education, skills, projects } = content;
  const fullName = `${personalInfo.firstName} ${personalInfo.lastName}`.trim();

  return (
    <div 
      className="w-full max-w-[8.5in] mx-auto bg-white shadow-lg"
      style={{ 
        fontFamily: template.fonts.body,
        color: template.colors.text,
        backgroundColor: template.colors.background,
        minHeight: '11in'
      }}
    >
      {/* Header Section */}
      <div 
        className="p-8 text-center"
        style={{ backgroundColor: template.colors.primary, color: template.colors.background }}
      >
        <h1 
          className="text-3xl font-bold mb-2"
          style={{ fontFamily: template.fonts.heading }}
        >
          {fullName || 'Your Name'}
        </h1>
        
        {/* Contact Information */}
        <div className="flex flex-wrap justify-center gap-4 text-sm opacity-90">
          {personalInfo.email && (
            <div className="flex items-center gap-1">
              <Mail className="w-4 h-4" />
              <span>{personalInfo.email}</span>
            </div>
          )}
          {personalInfo.phone && (
            <div className="flex items-center gap-1">
              <Phone className="w-4 h-4" />
              <span>{personalInfo.phone}</span>
            </div>
          )}
          {personalInfo.location && (
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              <span>{personalInfo.location}</span>
            </div>
          )}
        </div>

        {/* Online Presence */}
        {(personalInfo.website || personalInfo.linkedin || personalInfo.github) && (
          <div className="flex flex-wrap justify-center gap-4 text-sm mt-2 opacity-90">
            {personalInfo.website && (
              <div className="flex items-center gap-1">
                <Globe className="w-4 h-4" />
                <span>{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedin && (
              <div className="flex items-center gap-1">
                <Linkedin className="w-4 h-4" />
                <span>LinkedIn</span>
              </div>
            )}
            {personalInfo.github && (
              <div className="flex items-center gap-1">
                <Github className="w-4 h-4" />
                <span>GitHub</span>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="p-8 space-y-6">
        {/* Professional Summary */}
        {personalInfo.summary && (
          <section>
            <h2 
              className="text-xl font-bold mb-3 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Professional Summary
            </h2>
            <p className="text-sm leading-relaxed">{personalInfo.summary}</p>
          </section>
        )}

        {/* Work Experience */}
        {experience.length > 0 && (
          <section>
            <h2 
              className="text-xl font-bold mb-3 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Work Experience
            </h2>
            <div className="space-y-4">
              {experience.map((exp) => (
                <div key={exp.id} className="space-y-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-base">{exp.jobTitle}</h3>
                      <p className="text-sm" style={{ color: template.colors.secondary }}>
                        {exp.company} {exp.location && `• ${exp.location}`}
                      </p>
                    </div>
                    <div className="text-sm text-right" style={{ color: template.colors.secondary }}>
                      {exp.startDate && (
                        <p>
                          {new Date(exp.startDate).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'short' 
                          })} - {exp.current ? 'Present' : exp.endDate ? new Date(exp.endDate).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'short' 
                          }) : 'Present'}
                        </p>
                      )}
                    </div>
                  </div>
                  {exp.description && (
                    <p className="text-sm leading-relaxed">{exp.description}</p>
                  )}
                  {exp.achievements.length > 0 && (
                    <ul className="list-disc list-inside text-sm space-y-1 ml-4">
                      {exp.achievements.map((achievement, index) => (
                        <li key={index}>{achievement}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {education.length > 0 && (
          <section>
            <h2 
              className="text-xl font-bold mb-3 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Education
            </h2>
            <div className="space-y-3">
              {education.map((edu) => (
                <div key={edu.id} className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-base">{edu.degree}</h3>
                    <p className="text-sm" style={{ color: template.colors.secondary }}>
                      {edu.institution} {edu.location && `• ${edu.location}`}
                    </p>
                  </div>
                  <div className="text-sm text-right" style={{ color: template.colors.secondary }}>
                    {edu.startDate && (
                      <p>
                        {new Date(edu.startDate).toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'short' 
                        })} - {edu.current ? 'Present' : edu.endDate ? new Date(edu.endDate).toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'short' 
                        }) : 'Present'}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills */}
        {skills.length > 0 && (
          <section>
            <h2 
              className="text-xl font-bold mb-3 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Skills
            </h2>
            <div className="flex flex-wrap gap-2">
              {skills.map((skill) => (
                <span 
                  key={skill.id}
                  className="px-3 py-1 rounded-full text-sm"
                  style={{ 
                    backgroundColor: template.colors.accent + '20',
                    color: template.colors.primary
                  }}
                >
                  {skill.name}
                </span>
              ))}
            </div>
          </section>
        )}

        {/* Projects */}
        {projects.length > 0 && (
          <section>
            <h2 
              className="text-xl font-bold mb-3 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Projects
            </h2>
            <div className="space-y-3">
              {projects.map((project) => (
                <div key={project.id}>
                  <h3 className="font-semibold text-base">{project.name}</h3>
                  <p className="text-sm leading-relaxed">{project.description}</p>
                  {project.technologies.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {project.technologies.map((tech, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 rounded text-xs"
                          style={{ 
                            backgroundColor: template.colors.secondary + '20',
                            color: template.colors.secondary
                          }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}
