'use client';

import { useState } from 'react';
import { ResumeTemplate } from '@/types/resume';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Crown, Check } from 'lucide-react';

interface TemplateSelectorProps {
  templates: ResumeTemplate[];
  selectedTemplate: ResumeTemplate | null;
  onTemplateSelect: (template: ResumeTemplate) => void;
}

export function TemplateSelector({
  templates,
  selectedTemplate,
  onTemplateSelect,
}: TemplateSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'All Templates' },
    { id: 'modern', name: 'Modern' },
    { id: 'classic', name: 'Classic' },
    { id: 'creative', name: 'Creative' },
    { id: 'minimal', name: 'Minimal' },
  ];

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(template => template.category === selectedCategory);

  return (
    <div className="space-y-4">
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
          >
            {category.name}
          </Button>
        ))}
      </div>

      {/* Template Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.map((template) => (
          <Card
            key={template.id}
            className={`relative cursor-pointer transition-all hover:shadow-lg ${
              selectedTemplate?.id === template.id
                ? 'ring-2 ring-blue-500 shadow-lg'
                : 'hover:shadow-md'
            }`}
            onClick={() => onTemplateSelect(template)}
          >
            {/* Template Preview */}
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-50 to-gray-100 rounded-t-lg relative overflow-hidden">
              {template.previewImage ? (
                <img
                  src={template.previewImage}
                  alt={template.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div 
                  className="w-full h-full flex items-center justify-center"
                  style={{ backgroundColor: template.colors.background }}
                >
                  <div className="w-3/4 space-y-2">
                    <div 
                      className="h-3 rounded"
                      style={{ backgroundColor: template.colors.primary }}
                    />
                    <div 
                      className="h-2 w-2/3 rounded"
                      style={{ backgroundColor: template.colors.secondary }}
                    />
                    <div 
                      className="h-2 w-1/2 rounded"
                      style={{ backgroundColor: template.colors.secondary }}
                    />
                    <div className="space-y-1 mt-4">
                      <div 
                        className="h-1 rounded"
                        style={{ backgroundColor: template.colors.text }}
                      />
                      <div 
                        className="h-1 w-4/5 rounded"
                        style={{ backgroundColor: template.colors.text }}
                      />
                      <div 
                        className="h-1 w-3/5 rounded"
                        style={{ backgroundColor: template.colors.text }}
                      />
                    </div>
                  </div>
                </div>
              )}
              
              {/* Premium Badge */}
              {template.isPremium && (
                <div className="absolute top-2 right-2">
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    <Crown className="w-3 h-3 mr-1" />
                    Pro
                  </Badge>
                </div>
              )}

              {/* Selected Indicator */}
              {selectedTemplate?.id === template.id && (
                <div className="absolute top-2 left-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
            </div>

            {/* Template Info */}
            <div className="p-4">
              <h4 className="font-semibold text-sm mb-1">{template.name}</h4>
              <p className="text-xs text-gray-600 mb-2">{template.description}</p>
              
              {/* Category Badge */}
              <Badge variant="outline" className="text-xs">
                {template.category}
              </Badge>
            </div>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No templates found in this category.</p>
        </div>
      )}
    </div>
  );
}
