'use client';

import { ResumeContent, ResumeTemplate } from '@/types/resume';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

interface ModernTemplateProps {
  content: ResumeContent;
  template: ResumeTemplate;
}

export function ModernTemplate({ content, template }: ModernTemplateProps) {
  const { personalInfo, experience, education, skills, projects } = content;
  const fullName = `${personalInfo.firstName} ${personalInfo.lastName}`.trim();

  return (
    <div 
      className="w-full max-w-[8.5in] mx-auto bg-white shadow-lg grid grid-cols-3 gap-0"
      style={{ 
        fontFamily: template.fonts.body,
        color: template.colors.text,
        backgroundColor: template.colors.background,
        minHeight: '11in'
      }}
    >
      {/* Left Sidebar */}
      <div 
        className="col-span-1 p-6"
        style={{ backgroundColor: template.colors.primary, color: template.colors.background }}
      >
        {/* Profile Section */}
        <div className="mb-8">
          <h1 
            className="text-2xl font-bold mb-4"
            style={{ fontFamily: template.fonts.heading }}
          >
            {fullName || 'Your Name'}
          </h1>
          
          {/* Contact Information */}
          <div className="space-y-3 text-sm">
            {personalInfo.email && (
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 flex-shrink-0" />
                <span className="break-all">{personalInfo.email}</span>
              </div>
            )}
            {personalInfo.phone && (
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 flex-shrink-0" />
                <span>{personalInfo.phone}</span>
              </div>
            )}
            {personalInfo.location && (
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 flex-shrink-0" />
                <span>{personalInfo.location}</span>
              </div>
            )}
            {personalInfo.website && (
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 flex-shrink-0" />
                <span className="break-all">{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedin && (
              <div className="flex items-center gap-2">
                <Linkedin className="w-4 h-4 flex-shrink-0" />
                <span>LinkedIn</span>
              </div>
            )}
            {personalInfo.github && (
              <div className="flex items-center gap-2">
                <Github className="w-4 h-4 flex-shrink-0" />
                <span>GitHub</span>
              </div>
            )}
          </div>
        </div>

        {/* Skills */}
        {skills.length > 0 && (
          <div className="mb-8">
            <h2 
              className="text-lg font-bold mb-4 pb-2 border-b border-current"
              style={{ fontFamily: template.fonts.heading }}
            >
              Skills
            </h2>
            <div className="space-y-2">
              {skills.map((skill) => (
                <div key={skill.id} className="text-sm">
                  <div className="flex justify-between items-center mb-1">
                    <span>{skill.name}</span>
                    <span className="text-xs opacity-75">{skill.level}</span>
                  </div>
                  <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                    <div 
                      className="bg-white rounded-full h-2 transition-all"
                      style={{ 
                        width: skill.level === 'Expert' ? '100%' : 
                               skill.level === 'Advanced' ? '80%' : 
                               skill.level === 'Intermediate' ? '60%' : '40%'
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education */}
        {education.length > 0 && (
          <div className="mb-8">
            <h2 
              className="text-lg font-bold mb-4 pb-2 border-b border-current"
              style={{ fontFamily: template.fonts.heading }}
            >
              Education
            </h2>
            <div className="space-y-4">
              {education.map((edu) => (
                <div key={edu.id} className="text-sm">
                  <h3 className="font-semibold">{edu.degree}</h3>
                  <p className="opacity-90">{edu.institution}</p>
                  {edu.location && <p className="opacity-75 text-xs">{edu.location}</p>}
                  {edu.startDate && (
                    <p className="opacity-75 text-xs">
                      {new Date(edu.startDate).getFullYear()} - {
                        edu.current ? 'Present' : 
                        edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'
                      }
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Right Content */}
      <div className="col-span-2 p-6">
        {/* Professional Summary */}
        {personalInfo.summary && (
          <section className="mb-8">
            <h2 
              className="text-xl font-bold mb-4 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Professional Summary
            </h2>
            <p className="text-sm leading-relaxed">{personalInfo.summary}</p>
          </section>
        )}

        {/* Work Experience */}
        {experience.length > 0 && (
          <section className="mb-8">
            <h2 
              className="text-xl font-bold mb-4 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Work Experience
            </h2>
            <div className="space-y-6">
              {experience.map((exp) => (
                <div key={exp.id} className="space-y-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-base">{exp.jobTitle}</h3>
                      <p className="text-sm font-medium" style={{ color: template.colors.primary }}>
                        {exp.company}
                      </p>
                      {exp.location && (
                        <p className="text-xs" style={{ color: template.colors.secondary }}>
                          {exp.location}
                        </p>
                      )}
                    </div>
                    <div className="text-sm text-right" style={{ color: template.colors.secondary }}>
                      {exp.startDate && (
                        <p>
                          {new Date(exp.startDate).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'short' 
                          })} - {exp.current ? 'Present' : exp.endDate ? new Date(exp.endDate).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'short' 
                          }) : 'Present'}
                        </p>
                      )}
                    </div>
                  </div>
                  {exp.description && (
                    <p className="text-sm leading-relaxed">{exp.description}</p>
                  )}
                  {exp.achievements.length > 0 && (
                    <ul className="list-disc list-inside text-sm space-y-1 ml-4">
                      {exp.achievements.map((achievement, index) => (
                        <li key={index}>{achievement}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects */}
        {projects.length > 0 && (
          <section>
            <h2 
              className="text-xl font-bold mb-4 pb-2 border-b-2"
              style={{ 
                fontFamily: template.fonts.heading,
                color: template.colors.primary,
                borderColor: template.colors.primary
              }}
            >
              Projects
            </h2>
            <div className="space-y-4">
              {projects.map((project) => (
                <div key={project.id}>
                  <h3 className="font-semibold text-base">{project.name}</h3>
                  <p className="text-sm leading-relaxed">{project.description}</p>
                  {project.technologies.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {project.technologies.map((tech, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 rounded text-xs"
                          style={{ 
                            backgroundColor: template.colors.accent + '20',
                            color: template.colors.primary
                          }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}
