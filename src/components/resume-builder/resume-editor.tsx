'use client';

import { useState } from 'react';
import { ResumeContent } from '@/types/resume';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PersonalInfoEditor } from './sections/personal-info-editor';
import { ExperienceEditor } from './sections/experience-editor';
import { EducationEditor } from './sections/education-editor';
import { SkillsEditor } from './sections/skills-editor';
import { ProjectsEditor } from './sections/projects-editor';
import { 
  User, 
  Briefcase, 
  GraduationCap, 
  Code, 
  FolderOpen,
  Award,
  Languages,
  Plus,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ResumeEditorProps {
  content: ResumeContent;
  onContentChange: (content: ResumeContent) => void;
}

interface Section {
  id: string;
  title: string;
  icon: React.ReactNode;
  component: React.ComponentType<any>;
  isExpanded: boolean;
}

export function ResumeEditor({ content, onContentChange }: ResumeEditorProps) {
  const [sections, setSections] = useState<Section[]>([
    {
      id: 'personal',
      title: 'Personal Information',
      icon: <User className="w-4 h-4" />,
      component: PersonalInfoEditor,
      isExpanded: true,
    },
    {
      id: 'experience',
      title: 'Work Experience',
      icon: <Briefcase className="w-4 h-4" />,
      component: ExperienceEditor,
      isExpanded: false,
    },
    {
      id: 'education',
      title: 'Education',
      icon: <GraduationCap className="w-4 h-4" />,
      component: EducationEditor,
      isExpanded: false,
    },
    {
      id: 'skills',
      title: 'Skills',
      icon: <Code className="w-4 h-4" />,
      component: SkillsEditor,
      isExpanded: false,
    },
    {
      id: 'projects',
      title: 'Projects',
      icon: <FolderOpen className="w-4 h-4" />,
      component: ProjectsEditor,
      isExpanded: false,
    },
  ]);

  const toggleSection = (sectionId: string) => {
    setSections(sections.map(section => 
      section.id === sectionId 
        ? { ...section, isExpanded: !section.isExpanded }
        : section
    ));
  };

  const updatePersonalInfo = (personalInfo: ResumeContent['personalInfo']) => {
    onContentChange({ ...content, personalInfo });
  };

  const updateExperience = (experience: ResumeContent['experience']) => {
    onContentChange({ ...content, experience });
  };

  const updateEducation = (education: ResumeContent['education']) => {
    onContentChange({ ...content, education });
  };

  const updateSkills = (skills: ResumeContent['skills']) => {
    onContentChange({ ...content, skills });
  };

  const updateProjects = (projects: ResumeContent['projects']) => {
    onContentChange({ ...content, projects });
  };

  const getSectionProps = (sectionId: string) => {
    switch (sectionId) {
      case 'personal':
        return {
          data: content.personalInfo,
          onChange: updatePersonalInfo,
        };
      case 'experience':
        return {
          data: content.experience,
          onChange: updateExperience,
        };
      case 'education':
        return {
          data: content.education,
          onChange: updateEducation,
        };
      case 'skills':
        return {
          data: content.skills,
          onChange: updateSkills,
        };
      case 'projects':
        return {
          data: content.projects,
          onChange: updateProjects,
        };
      default:
        return {};
    }
  };

  return (
    <div className="space-y-4">
      {sections.map((section) => {
        const SectionComponent = section.component;
        const sectionProps = getSectionProps(section.id);

        return (
          <Card key={section.id}>
            <CardHeader 
              className="cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => toggleSection(section.id)}
            >
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {section.icon}
                  <span>{section.title}</span>
                </div>
                {section.isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </CardTitle>
            </CardHeader>
            
            {section.isExpanded && (
              <CardContent>
                <SectionComponent {...sectionProps} />
              </CardContent>
            )}
          </Card>
        );
      })}

      {/* Add Section Button */}
      <Card className="border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="flex items-center justify-center py-8">
          <Button variant="ghost" className="text-gray-500 hover:text-gray-700">
            <Plus className="w-4 h-4 mr-2" />
            Add Custom Section
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
