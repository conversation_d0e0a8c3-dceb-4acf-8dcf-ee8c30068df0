export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type ApplicationStatus =
  | 'applied'
  | 'viewed'
  | 'screening'
  | 'interview'
  | 'offer'
  | 'rejected'
  | 'withdrawn'

export type JobType =
  | 'full-time'
  | 'part-time'
  | 'contract'
  | 'internship'
  | 'remote'

export type ExperienceLevel =
  | 'entry'
  | 'mid'
  | 'senior'
  | 'executive'

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          phone: string | null
          location: string | null
          website: string | null
          linkedin_url: string | null
          github_url: string | null
          bio: string | null
          subscription_tier: string
          subscription_expires_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          location?: string | null
          website?: string | null
          linkedin_url?: string | null
          github_url?: string | null
          bio?: string | null
          subscription_tier?: string
          subscription_expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          location?: string | null
          website?: string | null
          linkedin_url?: string | null
          github_url?: string | null
          bio?: string | null
          subscription_tier?: string
          subscription_expires_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      resume_templates: {
        Row: {
          id: string
          name: string
          description: string | null
          category: string
          is_premium: boolean
          preview_image_url: string | null
          template_data: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          category: string
          is_premium?: boolean
          preview_image_url?: string | null
          template_data: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          category?: string
          is_premium?: boolean
          preview_image_url?: string | null
          template_data?: Json
          created_at?: string
          updated_at?: string
        }
      }
      resumes: {
        Row: {
          id: string
          user_id: string
          title: string
          template_id: string | null
          content: Json
          is_public: boolean
          is_default: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          template_id?: string | null
          content?: Json
          is_public?: boolean
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          template_id?: string | null
          content?: Json
          is_public?: boolean
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      companies: {
        Row: {
          id: string
          name: string
          website: string | null
          logo_url: string | null
          industry: string | null
          size: string | null
          location: string | null
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          website?: string | null
          logo_url?: string | null
          industry?: string | null
          size?: string | null
          location?: string | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          website?: string | null
          logo_url?: string | null
          industry?: string | null
          size?: string | null
          location?: string | null
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      jobs: {
        Row: {
          id: string
          company_id: string | null
          title: string
          description: string | null
          requirements: string[] | null
          benefits: string[] | null
          location: string | null
          job_type: JobType | null
          experience_level: ExperienceLevel | null
          salary_min: number | null
          salary_max: number | null
          salary_currency: string | null
          salary_period: string | null
          is_remote: boolean
          skills: string[] | null
          posted_date: string | null
          application_deadline: string | null
          job_url: string | null
          source: string | null
          external_id: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          company_id?: string | null
          title: string
          description?: string | null
          requirements?: string[] | null
          benefits?: string[] | null
          location?: string | null
          job_type?: JobType | null
          experience_level?: ExperienceLevel | null
          salary_min?: number | null
          salary_max?: number | null
          salary_currency?: string | null
          salary_period?: string | null
          is_remote?: boolean
          skills?: string[] | null
          posted_date?: string | null
          application_deadline?: string | null
          job_url?: string | null
          source?: string | null
          external_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          company_id?: string | null
          title?: string
          description?: string | null
          requirements?: string[] | null
          benefits?: string[] | null
          location?: string | null
          job_type?: JobType | null
          experience_level?: ExperienceLevel | null
          salary_min?: number | null
          salary_max?: number | null
          salary_currency?: string | null
          salary_period?: string | null
          is_remote?: boolean
          skills?: string[] | null
          posted_date?: string | null
          application_deadline?: string | null
          job_url?: string | null
          source?: string | null
          external_id?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      job_applications: {
        Row: {
          id: string
          user_id: string
          job_id: string | null
          resume_id: string | null
          company_name: string
          job_title: string
          job_url: string | null
          status: ApplicationStatus
          applied_at: string
          response_at: string | null
          interview_date: string | null
          notes: string | null
          cover_letter: string | null
          follow_up_dates: string[] | null
          rejection_reason: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          job_id?: string | null
          resume_id?: string | null
          company_name: string
          job_title: string
          job_url?: string | null
          status?: ApplicationStatus
          applied_at?: string
          response_at?: string | null
          interview_date?: string | null
          notes?: string | null
          cover_letter?: string | null
          follow_up_dates?: string[] | null
          rejection_reason?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          job_id?: string | null
          resume_id?: string | null
          company_name?: string
          job_title?: string
          job_url?: string | null
          status?: ApplicationStatus
          applied_at?: string
          response_at?: string | null
          interview_date?: string | null
          notes?: string | null
          cover_letter?: string | null
          follow_up_dates?: string[] | null
          rejection_reason?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      job_search_loops: {
        Row: {
          id: string
          user_id: string
          name: string
          search_filters: Json
          is_active: boolean
          auto_apply: boolean
          email_template: string | null
          last_run_at: string | null
          total_applications: number
          successful_applications: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          search_filters?: Json
          is_active?: boolean
          auto_apply?: boolean
          email_template?: string | null
          last_run_at?: string | null
          total_applications?: number
          successful_applications?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          search_filters?: Json
          is_active?: boolean
          auto_apply?: boolean
          email_template?: string | null
          last_run_at?: string | null
          total_applications?: number
          successful_applications?: number
          created_at?: string
          updated_at?: string
        }
      }
      email_templates: {
        Row: {
          id: string
          user_id: string
          name: string
          subject: string
          content: string
          is_default: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          subject: string
          content: string
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          subject?: string
          content?: string
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      application_events: {
        Row: {
          id: string
          application_id: string
          event_type: string
          event_data: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          application_id: string
          event_type: string
          event_data?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          application_id?: string
          event_type?: string
          event_data?: Json | null
          created_at?: string
        }
      }
      user_preferences: {
        Row: {
          id: string
          user_id: string
          email_notifications: boolean
          auto_apply_enabled: boolean
          preferred_job_types: JobType[] | null
          preferred_locations: string[] | null
          salary_expectations: Json | null
          excluded_companies: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email_notifications?: boolean
          auto_apply_enabled?: boolean
          preferred_job_types?: JobType[] | null
          preferred_locations?: string[] | null
          salary_expectations?: Json | null
          excluded_companies?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email_notifications?: boolean
          auto_apply_enabled?: boolean
          preferred_job_types?: JobType[] | null
          preferred_locations?: string[] | null
          salary_expectations?: Json | null
          excluded_companies?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_stats: {
        Args: {
          user_uuid: string
        }
        Returns: Json
      }
      generate_sample_applications: {
        Args: {
          user_uuid: string
          num_applications?: number
        }
        Returns: undefined
      }
    }
    Enums: {
      application_status: ApplicationStatus
      job_type: JobType
      experience_level: ExperienceLevel
      skill_level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert'
      language_proficiency: 'Basic' | 'Conversational' | 'Fluent' | 'Native'
    }
  }
}
