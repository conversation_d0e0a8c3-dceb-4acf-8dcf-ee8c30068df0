import { ResumeContent } from '@/types/resume';
import { Job } from '@/types/job';

export interface OptimizationSuggestion {
  id: string;
  type: 'content' | 'keyword' | 'format' | 'structure';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  suggestion: string;
  section: string;
  field?: string;
  currentValue?: string;
  suggestedValue?: string;
}

export interface ResumeScore {
  overall: number;
  sections: {
    personalInfo: number;
    summary: number;
    experience: number;
    education: number;
    skills: number;
  };
  improvements: OptimizationSuggestion[];
}

export class ResumeOptimizer {
  async analyzeResume(resume: ResumeContent): Promise<ResumeScore> {
    const suggestions: OptimizationSuggestion[] = [];
    
    // Analyze personal information
    const personalInfoScore = this.analyzePersonalInfo(resume.personalInfo, suggestions);
    
    // Analyze professional summary
    const summaryScore = this.analyzeSummary(resume.personalInfo.summary, suggestions);
    
    // Analyze work experience
    const experienceScore = this.analyzeExperience(resume.experience, suggestions);
    
    // Analyze education
    const educationScore = this.analyzeEducation(resume.education, suggestions);
    
    // Analyze skills
    const skillsScore = this.analyzeSkills(resume.skills, suggestions);
    
    // Calculate overall score
    const overall = Math.round(
      (personalInfoScore + summaryScore + experienceScore + educationScore + skillsScore) / 5
    );

    return {
      overall,
      sections: {
        personalInfo: personalInfoScore,
        summary: summaryScore,
        experience: experienceScore,
        education: educationScore,
        skills: skillsScore,
      },
      improvements: suggestions.sort((a, b) => {
        const severityOrder = { high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      }),
    };
  }

  async optimizeForJob(resume: ResumeContent, job: Job): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];
    
    // Analyze keyword matching
    this.analyzeKeywordMatch(resume, job, suggestions);
    
    // Analyze skills alignment
    this.analyzeSkillsAlignment(resume, job, suggestions);
    
    // Analyze experience relevance
    this.analyzeExperienceRelevance(resume, job, suggestions);
    
    return suggestions.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  private analyzePersonalInfo(personalInfo: any, suggestions: OptimizationSuggestion[]): number {
    let score = 100;
    
    if (!personalInfo.firstName || !personalInfo.lastName) {
      suggestions.push({
        id: 'missing-name',
        type: 'content',
        severity: 'high',
        title: 'Missing Name',
        description: 'Your resume should include your full name',
        suggestion: 'Add your first and last name to make your resume professional',
        section: 'personalInfo',
        field: 'name'
      });
      score -= 20;
    }
    
    if (!personalInfo.email) {
      suggestions.push({
        id: 'missing-email',
        type: 'content',
        severity: 'high',
        title: 'Missing Email',
        description: 'Contact email is essential for employers to reach you',
        suggestion: 'Add a professional email address',
        section: 'personalInfo',
        field: 'email'
      });
      score -= 20;
    }
    
    if (!personalInfo.phone) {
      suggestions.push({
        id: 'missing-phone',
        type: 'content',
        severity: 'medium',
        title: 'Missing Phone Number',
        description: 'Phone number provides an additional way for employers to contact you',
        suggestion: 'Add your phone number for better accessibility',
        section: 'personalInfo',
        field: 'phone'
      });
      score -= 10;
    }
    
    if (!personalInfo.location) {
      suggestions.push({
        id: 'missing-location',
        type: 'content',
        severity: 'medium',
        title: 'Missing Location',
        description: 'Location helps employers understand your availability for local positions',
        suggestion: 'Add your city and state/country',
        section: 'personalInfo',
        field: 'location'
      });
      score -= 10;
    }
    
    return Math.max(0, score);
  }

  private analyzeSummary(summary: string, suggestions: OptimizationSuggestion[]): number {
    let score = 100;
    
    if (!summary || summary.trim().length === 0) {
      suggestions.push({
        id: 'missing-summary',
        type: 'content',
        severity: 'high',
        title: 'Missing Professional Summary',
        description: 'A professional summary helps recruiters quickly understand your value proposition',
        suggestion: 'Add a 2-3 sentence summary highlighting your key skills and experience',
        section: 'personalInfo',
        field: 'summary'
      });
      return 0;
    }
    
    if (summary.length < 100) {
      suggestions.push({
        id: 'short-summary',
        type: 'content',
        severity: 'medium',
        title: 'Summary Too Short',
        description: 'Your professional summary could be more detailed',
        suggestion: 'Expand your summary to 100-200 words to better showcase your qualifications',
        section: 'personalInfo',
        field: 'summary',
        currentValue: summary
      });
      score -= 20;
    }
    
    if (summary.length > 300) {
      suggestions.push({
        id: 'long-summary',
        type: 'content',
        severity: 'low',
        title: 'Summary Too Long',
        description: 'Your professional summary might be too lengthy',
        suggestion: 'Consider condensing your summary to 200-250 words for better readability',
        section: 'personalInfo',
        field: 'summary',
        currentValue: summary
      });
      score -= 10;
    }
    
    return Math.max(0, score);
  }

  private analyzeExperience(experience: any[], suggestions: OptimizationSuggestion[]): number {
    let score = 100;
    
    if (experience.length === 0) {
      suggestions.push({
        id: 'no-experience',
        type: 'content',
        severity: 'high',
        title: 'No Work Experience',
        description: 'Work experience is crucial for most positions',
        suggestion: 'Add your work experience, including internships, part-time jobs, or volunteer work',
        section: 'experience'
      });
      return 0;
    }
    
    experience.forEach((exp, index) => {
      if (!exp.jobTitle) {
        suggestions.push({
          id: `missing-job-title-${index}`,
          type: 'content',
          severity: 'high',
          title: 'Missing Job Title',
          description: `Experience entry ${index + 1} is missing a job title`,
          suggestion: 'Add the job title for this position',
          section: 'experience'
        });
        score -= 15;
      }
      
      if (!exp.company) {
        suggestions.push({
          id: `missing-company-${index}`,
          type: 'content',
          severity: 'high',
          title: 'Missing Company Name',
          description: `Experience entry ${index + 1} is missing a company name`,
          suggestion: 'Add the company name for this position',
          section: 'experience'
        });
        score -= 15;
      }
      
      if (!exp.description || exp.description.length < 50) {
        suggestions.push({
          id: `short-description-${index}`,
          type: 'content',
          severity: 'medium',
          title: 'Insufficient Job Description',
          description: `Experience entry ${index + 1} needs a more detailed description`,
          suggestion: 'Add a detailed description of your responsibilities and achievements',
          section: 'experience'
        });
        score -= 10;
      }
      
      if (exp.achievements.length === 0) {
        suggestions.push({
          id: `no-achievements-${index}`,
          type: 'content',
          severity: 'medium',
          title: 'Missing Achievements',
          description: `Experience entry ${index + 1} could benefit from specific achievements`,
          suggestion: 'Add quantifiable achievements and accomplishments for this role',
          section: 'experience'
        });
        score -= 10;
      }
    });
    
    return Math.max(0, score);
  }

  private analyzeEducation(education: any[], suggestions: OptimizationSuggestion[]): number {
    let score = 100;
    
    if (education.length === 0) {
      suggestions.push({
        id: 'no-education',
        type: 'content',
        severity: 'medium',
        title: 'No Education Information',
        description: 'Education information helps establish your qualifications',
        suggestion: 'Add your educational background, including degrees, certifications, or relevant courses',
        section: 'education'
      });
      score -= 30;
    }
    
    return Math.max(0, score);
  }

  private analyzeSkills(skills: any[], suggestions: OptimizationSuggestion[]): number {
    let score = 100;
    
    if (skills.length === 0) {
      suggestions.push({
        id: 'no-skills',
        type: 'content',
        severity: 'high',
        title: 'No Skills Listed',
        description: 'Skills section is essential for showcasing your capabilities',
        suggestion: 'Add relevant technical and soft skills for your target role',
        section: 'skills'
      });
      return 0;
    }
    
    if (skills.length < 5) {
      suggestions.push({
        id: 'few-skills',
        type: 'content',
        severity: 'medium',
        title: 'Limited Skills',
        description: 'Consider adding more skills to better showcase your capabilities',
        suggestion: 'Add 5-10 relevant skills including both technical and soft skills',
        section: 'skills'
      });
      score -= 20;
    }
    
    return Math.max(0, score);
  }

  private analyzeKeywordMatch(resume: ResumeContent, job: Job, suggestions: OptimizationSuggestion[]): void {
    const jobKeywords = [
      ...job.skills,
      ...job.requirements.flatMap(req => req.toLowerCase().split(' ')),
      job.title.toLowerCase().split(' ')
    ].filter(keyword => keyword.length > 2);

    const resumeText = [
      resume.personalInfo.summary,
      ...resume.experience.map(exp => `${exp.jobTitle} ${exp.description} ${exp.achievements.join(' ')}`),
      ...resume.skills.map(skill => skill.name)
    ].join(' ').toLowerCase();

    const missingKeywords = jobKeywords.filter(keyword => 
      !resumeText.includes(keyword.toLowerCase())
    );

    if (missingKeywords.length > 0) {
      suggestions.push({
        id: 'missing-keywords',
        type: 'keyword',
        severity: 'high',
        title: 'Missing Job Keywords',
        description: `Your resume is missing ${missingKeywords.length} important keywords from the job posting`,
        suggestion: `Consider incorporating these keywords: ${missingKeywords.slice(0, 5).join(', ')}`,
        section: 'overall'
      });
    }
  }

  private analyzeSkillsAlignment(resume: ResumeContent, job: Job, suggestions: OptimizationSuggestion[]): void {
    const jobSkills = job.skills.map(skill => skill.toLowerCase());
    const resumeSkills = resume.skills.map(skill => skill.name.toLowerCase());
    
    const missingSkills = jobSkills.filter(skill => 
      !resumeSkills.some(resumeSkill => resumeSkill.includes(skill))
    );

    if (missingSkills.length > 0) {
      suggestions.push({
        id: 'missing-skills',
        type: 'content',
        severity: 'high',
        title: 'Missing Required Skills',
        description: `Your resume is missing ${missingSkills.length} skills mentioned in the job posting`,
        suggestion: `Consider adding these skills if you have experience: ${missingSkills.slice(0, 3).join(', ')}`,
        section: 'skills'
      });
    }
  }

  private analyzeExperienceRelevance(resume: ResumeContent, job: Job, suggestions: OptimizationSuggestion[]): void {
    const jobTitle = job.title.toLowerCase();
    const hasRelevantExperience = resume.experience.some(exp => 
      exp.jobTitle.toLowerCase().includes(jobTitle.split(' ')[0]) ||
      exp.description.toLowerCase().includes(jobTitle.split(' ')[0])
    );

    if (!hasRelevantExperience) {
      suggestions.push({
        id: 'irrelevant-experience',
        type: 'content',
        severity: 'medium',
        title: 'Limited Relevant Experience',
        description: 'Your experience section could better highlight relevant roles',
        suggestion: `Emphasize experience related to ${job.title} and similar roles`,
        section: 'experience'
      });
    }
  }

  async generateOptimizedSummary(resume: ResumeContent, job?: Job): Promise<string> {
    // This would typically call an AI API like OpenAI
    // For now, we'll return a template-based summary
    
    const skills = resume.skills.slice(0, 3).map(skill => skill.name).join(', ');
    const experience = resume.experience[0];
    
    if (job) {
      return `Experienced ${job.title} with expertise in ${skills}. Proven track record in ${experience?.company || 'previous roles'} with strong background in ${job.skills.slice(0, 2).join(' and ')}. Passionate about ${job.companyInfo?.industry || 'technology'} and committed to delivering high-quality results.`;
    }
    
    return `Professional with expertise in ${skills}. Experienced ${experience?.jobTitle || 'professional'} with a proven track record of success. Strong background in ${resume.skills.slice(3, 5).map(s => s.name).join(' and ')} with excellent problem-solving and communication skills.`;
  }
}
