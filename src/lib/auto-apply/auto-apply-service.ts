import { Job, JobSearchLoop, JobApplication } from '@/types/job';
import { JobAggregator } from '@/lib/job-apis/job-aggregator';
import { supabase } from '@/lib/supabase/client';

export interface AutoApplyConfig {
  userId: string;
  searchLoop: JobSearchLoop;
  resumeId: string;
  coverLetterTemplate: string;
  maxApplicationsPerDay: number;
  excludeAppliedJobs: boolean;
}

export interface ApplicationResult {
  jobId: string;
  success: boolean;
  error?: string;
  applicationId?: string;
}

export class AutoApplyService {
  private jobAggregator: JobAggregator;

  constructor() {
    this.jobAggregator = new JobAggregator();
  }

  async runAutoApply(config: AutoApplyConfig): Promise<ApplicationResult[]> {
    const results: ApplicationResult[] = [];
    
    try {
      // Get jobs based on search filters
      const jobs = await this.jobAggregator.searchJobs(config.searchLoop.search_filters as any);
      
      // Filter out already applied jobs if enabled
      let filteredJobs = jobs;
      if (config.excludeAppliedJobs) {
        filteredJobs = await this.filterAppliedJobs(jobs, config.userId);
      }

      // Limit applications per day
      const applicationsToday = await this.getApplicationsToday(config.userId);
      const remainingApplications = Math.max(0, config.maxApplicationsPerDay - applicationsToday);
      
      if (remainingApplications === 0) {
        console.log('Daily application limit reached');
        return results;
      }

      // Apply to jobs (up to the daily limit)
      const jobsToApply = filteredJobs.slice(0, remainingApplications);
      
      for (const job of jobsToApply) {
        try {
          const result = await this.applyToJob(job, config);
          results.push(result);
          
          // Add delay between applications to avoid rate limiting
          await this.delay(2000);
        } catch (error) {
          console.error(`Failed to apply to job ${job.id}:`, error);
          results.push({
            jobId: job.id,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Update search loop statistics
      await this.updateSearchLoopStats(config.searchLoop.id, results);

    } catch (error) {
      console.error('Auto-apply run failed:', error);
      throw error;
    }

    return results;
  }

  private async applyToJob(job: Job, config: AutoApplyConfig): Promise<ApplicationResult> {
    try {
      // Generate personalized cover letter
      const coverLetter = await this.generateCoverLetter(job, config.coverLetterTemplate);
      
      // Simulate job application process
      const applicationSuccess = await this.submitApplication(job, {
        resumeId: config.resumeId,
        coverLetter,
        userId: config.userId
      });

      if (applicationSuccess) {
        // Record the application in database
        const { data: application, error } = await supabase
          .from('job_applications')
          .insert({
            user_id: config.userId,
            job_id: job.id,
            resume_id: config.resumeId,
            company_name: job.company,
            job_title: job.title,
            job_url: job.url,
            status: 'applied',
            cover_letter: coverLetter,
            applied_at: new Date().toISOString(),
            notes: `Auto-applied via search loop: ${config.searchLoop.name}`
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to record application: ${error.message}`);
        }

        // Log application event
        await supabase
          .from('application_events')
          .insert({
            application_id: application.id,
            event_type: 'auto_applied',
            event_data: {
              search_loop_id: config.searchLoop.id,
              job_source: job.source
            }
          });

        return {
          jobId: job.id,
          success: true,
          applicationId: application.id
        };
      } else {
        return {
          jobId: job.id,
          success: false,
          error: 'Application submission failed'
        };
      }
    } catch (error) {
      return {
        jobId: job.id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async submitApplication(job: Job, applicationData: {
    resumeId: string;
    coverLetter: string;
    userId: string;
  }): Promise<boolean> {
    // Simulate application submission
    // In a real implementation, this would integrate with job board APIs
    
    // Simulate different success rates based on job source
    const successRates = {
      'company': 0.8,
      'linkedin': 0.7,
      'indeed': 0.6,
      'glassdoor': 0.65,
      'other': 0.5
    };

    const successRate = successRates[job.source as keyof typeof successRates] || 0.5;
    const isSuccess = Math.random() < successRate;

    // Simulate processing time
    await this.delay(1000 + Math.random() * 2000);

    return isSuccess;
  }

  private async generateCoverLetter(job: Job, template: string): Promise<string> {
    // Simple template replacement
    // In a real implementation, this would use AI to generate personalized cover letters
    
    let coverLetter = template;
    
    // Replace placeholders
    coverLetter = coverLetter.replace(/\{company\}/g, job.company);
    coverLetter = coverLetter.replace(/\{position\}/g, job.title);
    coverLetter = coverLetter.replace(/\{location\}/g, job.location);
    
    // Add job-specific skills if mentioned
    if (job.skills.length > 0) {
      const relevantSkills = job.skills.slice(0, 3).join(', ');
      coverLetter = coverLetter.replace(/\{skills\}/g, relevantSkills);
    }

    return coverLetter;
  }

  private async filterAppliedJobs(jobs: Job[], userId: string): Promise<Job[]> {
    try {
      const { data: appliedJobs, error } = await supabase
        .from('job_applications')
        .select('job_id, company_name, job_title')
        .eq('user_id', userId);

      if (error) {
        console.error('Failed to fetch applied jobs:', error);
        return jobs; // Return all jobs if we can't filter
      }

      const appliedJobIds = new Set(appliedJobs?.map(app => app.job_id) || []);
      const appliedCompanyPositions = new Set(
        appliedJobs?.map(app => `${app.company_name}:${app.job_title}`) || []
      );

      return jobs.filter(job => {
        // Filter by job ID
        if (appliedJobIds.has(job.id)) {
          return false;
        }

        // Filter by company + position combination
        const companyPosition = `${job.company}:${job.title}`;
        if (appliedCompanyPositions.has(companyPosition)) {
          return false;
        }

        return true;
      });
    } catch (error) {
      console.error('Error filtering applied jobs:', error);
      return jobs;
    }
  }

  private async getApplicationsToday(userId: string): Promise<number> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { count, error } = await supabase
        .from('job_applications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .gte('applied_at', today.toISOString());

      if (error) {
        console.error('Failed to get today\'s applications:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error getting applications today:', error);
      return 0;
    }
  }

  private async updateSearchLoopStats(
    searchLoopId: string, 
    results: ApplicationResult[]
  ): Promise<void> {
    try {
      const successfulApplications = results.filter(r => r.success).length;
      
      const { error } = await supabase
        .from('job_search_loops')
        .update({
          last_run_at: new Date().toISOString(),
          total_applications: supabase.sql`total_applications + ${results.length}`,
          successful_applications: supabase.sql`successful_applications + ${successfulApplications}`
        })
        .eq('id', searchLoopId);

      if (error) {
        console.error('Failed to update search loop stats:', error);
      }
    } catch (error) {
      console.error('Error updating search loop stats:', error);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getAutoApplyStatus(userId: string): Promise<{
    applicationsToday: number;
    activeLoops: number;
    totalApplications: number;
    successRate: number;
  }> {
    try {
      const [applicationsToday, activeLoops, totalStats] = await Promise.all([
        this.getApplicationsToday(userId),
        this.getActiveLoopsCount(userId),
        this.getTotalApplicationStats(userId)
      ]);

      return {
        applicationsToday,
        activeLoops,
        totalApplications: totalStats.total,
        successRate: totalStats.successRate
      };
    } catch (error) {
      console.error('Error getting auto-apply status:', error);
      return {
        applicationsToday: 0,
        activeLoops: 0,
        totalApplications: 0,
        successRate: 0
      };
    }
  }

  private async getActiveLoopsCount(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('job_search_loops')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_active', true)
      .eq('auto_apply', true);

    return count || 0;
  }

  private async getTotalApplicationStats(userId: string): Promise<{
    total: number;
    successRate: number;
  }> {
    const { data, error } = await supabase
      .from('job_applications')
      .select('status')
      .eq('user_id', userId);

    if (error || !data) {
      return { total: 0, successRate: 0 };
    }

    const total = data.length;
    const successful = data.filter(app => 
      ['interview', 'offer'].includes(app.status)
    ).length;

    const successRate = total > 0 ? (successful / total) * 100 : 0;

    return { total, successRate };
  }
}
