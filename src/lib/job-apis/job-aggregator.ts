import { Job, JobSearchFilters } from '@/types/job';

// Mock job data for demonstration
const mockJobs: Job[] = [
  {
    id: '1',
    title: 'Senior Software Engineer',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    type: 'full-time',
    salary: {
      min: 150000,
      max: 200000,
      currency: 'USD',
      period: 'yearly'
    },
    description: 'We are looking for a Senior Software Engineer to join our growing team. You will be responsible for designing, developing, and maintaining scalable web applications using modern technologies.',
    requirements: [
      '5+ years of software development experience',
      'Proficiency in JavaScript/TypeScript',
      'Experience with React and Node.js',
      'Strong problem-solving skills',
      'Experience with cloud platforms (AWS/GCP/Azure)'
    ],
    benefits: [
      'Competitive salary and equity',
      'Health, dental, and vision insurance',
      'Flexible work arrangements',
      '401(k) with company matching',
      'Professional development budget'
    ],
    postedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://example.com/jobs/1',
    source: 'company',
    isRemote: true,
    experienceLevel: 'senior',
    skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'AWS'],
    companyInfo: {
      size: '100-500',
      industry: 'Technology',
      website: 'https://techcorp.com',
      logo: 'https://via.placeholder.com/100x100?text=TC'
    }
  },
  {
    id: '2',
    title: 'Product Manager',
    company: 'InnovateLabs',
    location: 'New York, NY',
    type: 'full-time',
    salary: {
      min: 120000,
      max: 160000,
      currency: 'USD',
      period: 'yearly'
    },
    description: 'Join our product team to drive the strategy and execution of our core platform. You will work closely with engineering, design, and business teams to deliver exceptional user experiences.',
    requirements: [
      '3+ years of product management experience',
      'Experience with B2B SaaS products',
      'Strong analytical and communication skills',
      'Experience with agile development methodologies',
      'Data-driven decision making'
    ],
    benefits: [
      'Competitive compensation package',
      'Comprehensive health benefits',
      'Remote-first culture',
      'Learning and development opportunities',
      'Stock options'
    ],
    postedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://example.com/jobs/2',
    source: 'linkedin',
    isRemote: false,
    experienceLevel: 'mid',
    skills: ['Product Management', 'Analytics', 'Agile', 'Strategy'],
    companyInfo: {
      size: '50-100',
      industry: 'Technology',
      website: 'https://innovatelabs.com',
      logo: 'https://via.placeholder.com/100x100?text=IL'
    }
  },
  {
    id: '3',
    title: 'UX Designer',
    company: 'DesignStudio',
    location: 'Austin, TX',
    type: 'full-time',
    salary: {
      min: 90000,
      max: 130000,
      currency: 'USD',
      period: 'yearly'
    },
    description: 'We are seeking a talented UX Designer to create intuitive and engaging user experiences for our digital products. You will collaborate with cross-functional teams to solve complex design challenges.',
    requirements: [
      '2+ years of UX design experience',
      'Proficiency in Figma and design systems',
      'Strong portfolio demonstrating UX process',
      'Experience with user research and testing',
      'Understanding of accessibility principles'
    ],
    benefits: [
      'Creative and collaborative environment',
      'Health and wellness benefits',
      'Flexible working hours',
      'Professional development budget',
      'Modern design tools and equipment'
    ],
    postedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://example.com/jobs/3',
    source: 'indeed',
    isRemote: true,
    experienceLevel: 'mid',
    skills: ['UX Design', 'Figma', 'User Research', 'Prototyping', 'Accessibility'],
    companyInfo: {
      size: '10-50',
      industry: 'Design',
      website: 'https://designstudio.com',
      logo: 'https://via.placeholder.com/100x100?text=DS'
    }
  },
  {
    id: '4',
    title: 'Data Scientist',
    company: 'DataTech Solutions',
    location: 'Seattle, WA',
    type: 'full-time',
    salary: {
      min: 130000,
      max: 180000,
      currency: 'USD',
      period: 'yearly'
    },
    description: 'Join our data science team to build machine learning models and extract insights from large datasets. You will work on challenging problems across various domains.',
    requirements: [
      'PhD or Masters in Data Science, Statistics, or related field',
      'Strong programming skills in Python and R',
      'Experience with machine learning frameworks',
      'Knowledge of statistical analysis and modeling',
      'Experience with big data technologies'
    ],
    benefits: [
      'Competitive salary and bonuses',
      'Comprehensive benefits package',
      'Research and conference budget',
      'Flexible work arrangements',
      'Cutting-edge technology stack'
    ],
    postedDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://example.com/jobs/4',
    source: 'glassdoor',
    isRemote: true,
    experienceLevel: 'senior',
    skills: ['Python', 'R', 'Machine Learning', 'Statistics', 'Big Data'],
    companyInfo: {
      size: '200-500',
      industry: 'Technology',
      website: 'https://datatech.com',
      logo: 'https://via.placeholder.com/100x100?text=DT'
    }
  },
  {
    id: '5',
    title: 'Frontend Developer',
    company: 'WebCraft Agency',
    location: 'Los Angeles, CA',
    type: 'contract',
    salary: {
      min: 75,
      max: 100,
      currency: 'USD',
      period: 'hourly'
    },
    description: 'We need a skilled Frontend Developer to help build modern web applications for our clients. You will work with the latest technologies and frameworks.',
    requirements: [
      '3+ years of frontend development experience',
      'Expert knowledge of HTML, CSS, and JavaScript',
      'Experience with React or Vue.js',
      'Understanding of responsive design',
      'Experience with version control (Git)'
    ],
    benefits: [
      'Competitive hourly rate',
      'Flexible schedule',
      'Remote work options',
      'Opportunity to work on diverse projects',
      'Potential for full-time conversion'
    ],
    postedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    url: 'https://example.com/jobs/5',
    source: 'company',
    isRemote: true,
    experienceLevel: 'mid',
    skills: ['HTML', 'CSS', 'JavaScript', 'React', 'Vue.js'],
    companyInfo: {
      size: '10-50',
      industry: 'Digital Agency',
      website: 'https://webcraft.com',
      logo: 'https://via.placeholder.com/100x100?text=WC'
    }
  }
];

export class JobAggregator {
  async searchJobs(filters: JobSearchFilters): Promise<Job[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    let filteredJobs = [...mockJobs];

    // Apply filters
    if (filters.keywords.length > 0) {
      filteredJobs = filteredJobs.filter(job => 
        filters.keywords.some(keyword => 
          job.title.toLowerCase().includes(keyword.toLowerCase()) ||
          job.description.toLowerCase().includes(keyword.toLowerCase()) ||
          job.skills.some(skill => skill.toLowerCase().includes(keyword.toLowerCase()))
        )
      );
    }

    if (filters.location) {
      filteredJobs = filteredJobs.filter(job => 
        job.location.toLowerCase().includes(filters.location.toLowerCase()) ||
        (filters.remote && job.isRemote)
      );
    }

    if (filters.jobType.length > 0) {
      filteredJobs = filteredJobs.filter(job => 
        filters.jobType.includes(job.type)
      );
    }

    if (filters.experienceLevel.length > 0) {
      filteredJobs = filteredJobs.filter(job => 
        filters.experienceLevel.includes(job.experienceLevel)
      );
    }

    if (filters.salaryMin && job.salary) {
      filteredJobs = filteredJobs.filter(job => 
        job.salary && job.salary.min >= filters.salaryMin!
      );
    }

    if (filters.salaryMax && job.salary) {
      filteredJobs = filteredJobs.filter(job => 
        job.salary && job.salary.max <= filters.salaryMax!
      );
    }

    if (filters.remote) {
      filteredJobs = filteredJobs.filter(job => job.isRemote);
    }

    if (filters.companies.length > 0) {
      filteredJobs = filteredJobs.filter(job => 
        filters.companies.includes(job.company)
      );
    }

    if (filters.excludeCompanies.length > 0) {
      filteredJobs = filteredJobs.filter(job => 
        !filters.excludeCompanies.includes(job.company)
      );
    }

    // Apply date filter
    if (filters.datePosted !== 'any') {
      const daysAgo = {
        '1day': 1,
        '3days': 3,
        '7days': 7,
        '14days': 14,
        '30days': 30
      }[filters.datePosted] || 30;

      const cutoffDate = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000);
      filteredJobs = filteredJobs.filter(job => 
        new Date(job.postedDate) >= cutoffDate
      );
    }

    return filteredJobs;
  }

  async getJobById(id: string): Promise<Job | null> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockJobs.find(job => job.id === id) || null;
  }

  async getTrendingSkills(): Promise<string[]> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [
      'JavaScript', 'Python', 'React', 'Node.js', 'TypeScript',
      'AWS', 'Docker', 'Kubernetes', 'Machine Learning', 'Data Science'
    ];
  }

  async getPopularCompanies(): Promise<string[]> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [
      'Google', 'Microsoft', 'Apple', 'Amazon', 'Meta',
      'Netflix', 'Tesla', 'Spotify', 'Airbnb', 'Uber'
    ];
  }
}
