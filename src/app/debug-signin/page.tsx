'use client';

import { useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react';

export default function DebugSignInPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  const testSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    const debug: any = {
      timestamp: new Date().toISOString(),
      steps: []
    };

    try {
      // Step 1: Check environment
      debug.steps.push({
        step: 'Environment Check',
        status: 'info',
        data: {
          supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          anonKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length
        }
      });

      // Step 2: Test basic connection
      try {
        const { data: healthCheck } = await supabase.from('users').select('count').limit(1);
        debug.steps.push({
          step: 'Database Connection',
          status: 'success',
          data: 'Database accessible'
        });
      } catch (dbError: any) {
        debug.steps.push({
          step: 'Database Connection',
          status: 'error',
          data: dbError.message
        });
      }

      // Step 3: Attempt sign in
      debug.steps.push({
        step: 'Sign In Attempt',
        status: 'info',
        data: { email, passwordLength: password.length }
      });

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        debug.steps.push({
          step: 'Sign In Result',
          status: 'error',
          data: {
            error: error.message,
            code: error.status,
            details: error
          }
        });
      } else {
        debug.steps.push({
          step: 'Sign In Result',
          status: 'success',
          data: {
            hasUser: !!data.user,
            hasSession: !!data.session,
            userEmail: data.user?.email,
            emailConfirmed: !!data.user?.email_confirmed_at,
            userId: data.user?.id
          }
        });

        // Step 4: Check session after sign in
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        debug.steps.push({
          step: 'Session Check',
          status: sessionError ? 'error' : 'success',
          data: sessionError ? sessionError.message : {
            hasSession: !!sessionData.session,
            sessionUserId: sessionData.session?.user?.id
          }
        });

        // Step 5: Check user profile
        if (data.user) {
          try {
            const { data: profile, error: profileError } = await supabase
              .from('users')
              .select('*')
              .eq('id', data.user.id)
              .single();

            debug.steps.push({
              step: 'User Profile Check',
              status: profileError ? 'error' : 'success',
              data: profileError ? profileError.message : {
                hasProfile: !!profile,
                profileEmail: profile?.email,
                profileName: profile?.full_name
              }
            });
          } catch (profileError: any) {
            debug.steps.push({
              step: 'User Profile Check',
              status: 'error',
              data: profileError.message
            });
          }
        }
      }

    } catch (error: any) {
      debug.steps.push({
        step: 'Unexpected Error',
        status: 'error',
        data: error.message
      });
    }

    setDebugInfo(debug);
    setIsLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Debug Sign In</h1>
          <p className="text-gray-600">Test sign in and see detailed debug information</p>
        </div>

        {/* Sign In Form */}
        <Card>
          <CardHeader>
            <CardTitle>Test Sign In</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={testSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                />
              </div>
              
              <Button type="submit" disabled={isLoading} className="w-full">
                {isLoading ? 'Testing Sign In...' : 'Test Sign In'}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Debug Results */}
        {debugInfo && (
          <Card>
            <CardHeader>
              <CardTitle>Debug Results</CardTitle>
              <p className="text-sm text-gray-600">
                Test completed at: {new Date(debugInfo.timestamp).toLocaleString()}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {debugInfo.steps.map((step: any, index: number) => (
                <div
                  key={index}
                  className={`p-4 border rounded-lg ${getStatusColor(step.status)}`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {getStatusIcon(step.status)}
                    <h3 className="font-medium">{step.step}</h3>
                  </div>
                  
                  <div className="text-sm">
                    {typeof step.data === 'string' ? (
                      <p>{step.data}</p>
                    ) : (
                      <pre className="whitespace-pre-wrap bg-white p-2 rounded text-xs overflow-auto">
                        {JSON.stringify(step.data, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Quick Fixes */}
        <Card>
          <CardHeader>
            <CardTitle>Common Issues & Fixes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">If you see "Invalid login credentials":</h4>
              <ul className="text-sm space-y-1 ml-4">
                <li>• Check that you're using the correct email and password</li>
                <li>• Make sure your account exists (try signing up first)</li>
                <li>• Verify your email is confirmed</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">If you see database connection errors:</h4>
              <ul className="text-sm space-y-1 ml-4">
                <li>• Run the database setup scripts in Supabase SQL Editor</li>
                <li>• Check your environment variables</li>
                <li>• Verify your Supabase project is active</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">If sign in succeeds but profile is missing:</h4>
              <ul className="text-sm space-y-1 ml-4">
                <li>• The user profile wasn't created automatically</li>
                <li>• Run the RLS policies script to set up the user creation trigger</li>
                <li>• Manually create the profile in the users table</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
