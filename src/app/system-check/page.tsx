'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw, Database, Shield, Mail } from 'lucide-react';

interface CheckResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
  fix?: string;
}

export default function SystemCheckPage() {
  const [checks, setChecks] = useState<CheckResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [summary, setSummary] = useState({ pass: 0, fail: 0, warning: 0 });

  const runSystemCheck = async () => {
    setIsRunning(true);
    const results: CheckResult[] = [];

    // 1. Environment Variables Check
    try {
      const hasUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL;
      const hasKey = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      const urlValid = process.env.NEXT_PUBLIC_SUPABASE_URL?.includes('supabase.co');
      
      if (hasUrl && hasKey && urlValid) {
        results.push({
          name: 'Environment Variables',
          status: 'pass',
          message: 'Supabase environment variables are configured correctly',
          details: {
            url: process.env.NEXT_PUBLIC_SUPABASE_URL,
            keyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length
          }
        });
      } else {
        results.push({
          name: 'Environment Variables',
          status: 'fail',
          message: 'Missing or invalid Supabase environment variables',
          details: { hasUrl, hasKey, urlValid },
          fix: 'Check your .env.local file and ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set correctly'
        });
      }
    } catch (error) {
      results.push({
        name: 'Environment Variables',
        status: 'fail',
        message: 'Error checking environment variables',
        details: error
      });
    }

    // 2. Supabase Client Connection
    try {
      const client = supabase;
      results.push({
        name: 'Supabase Client',
        status: 'pass',
        message: 'Supabase client initialized successfully'
      });
    } catch (error: any) {
      results.push({
        name: 'Supabase Client',
        status: 'fail',
        message: 'Failed to initialize Supabase client',
        details: error.message,
        fix: 'Check your Supabase configuration and environment variables'
      });
    }

    // 3. Database Connection Test
    try {
      const { data, error } = await supabase.from('users').select('count').limit(1);
      
      if (error) {
        if (error.message.includes('relation "public.users" does not exist')) {
          results.push({
            name: 'Database Tables',
            status: 'fail',
            message: 'Database tables do not exist',
            details: error.message,
            fix: 'Run the database setup scripts (supabase-setup-safe.sql) in your Supabase SQL Editor'
          });
        } else {
          results.push({
            name: 'Database Connection',
            status: 'fail',
            message: 'Database connection failed',
            details: error.message,
            fix: 'Check your Supabase project status and database configuration'
          });
        }
      } else {
        results.push({
          name: 'Database Connection',
          status: 'pass',
          message: 'Successfully connected to database'
        });
      }
    } catch (error: any) {
      results.push({
        name: 'Database Connection',
        status: 'fail',
        message: 'Database connection error',
        details: error.message
      });
    }

    // 4. Check Required Tables
    const requiredTables = ['users', 'resumes', 'resume_templates', 'jobs', 'job_applications'];
    for (const table of requiredTables) {
      try {
        const { error } = await supabase.from(table).select('count').limit(1);
        
        if (error) {
          results.push({
            name: `Table: ${table}`,
            status: 'fail',
            message: `Table '${table}' does not exist or is not accessible`,
            details: error.message,
            fix: 'Run the database setup scripts in Supabase SQL Editor'
          });
        } else {
          results.push({
            name: `Table: ${table}`,
            status: 'pass',
            message: `Table '${table}' exists and is accessible`
          });
        }
      } catch (error: any) {
        results.push({
          name: `Table: ${table}`,
          status: 'fail',
          message: `Error checking table '${table}'`,
          details: error.message
        });
      }
    }

    // 5. Authentication Service Test
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        results.push({
          name: 'Authentication Service',
          status: 'fail',
          message: 'Authentication service error',
          details: error.message,
          fix: 'Check Supabase authentication configuration'
        });
      } else {
        results.push({
          name: 'Authentication Service',
          status: 'pass',
          message: 'Authentication service is working',
          details: { hasSession: !!session }
        });
      }
    } catch (error: any) {
      results.push({
        name: 'Authentication Service',
        status: 'fail',
        message: 'Authentication service connection failed',
        details: error.message
      });
    }

    // 6. Check Auth Configuration
    try {
      // Try to get auth settings (this will fail if auth is misconfigured)
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error && !error.message.includes('session_not_found')) {
        results.push({
          name: 'Auth Configuration',
          status: 'fail',
          message: 'Authentication configuration issue',
          details: error.message,
          fix: 'Check Supabase authentication settings and site URL configuration'
        });
      } else {
        results.push({
          name: 'Auth Configuration',
          status: 'pass',
          message: 'Authentication configuration is valid',
          details: { currentUser: user?.email || 'No user signed in' }
        });
      }
    } catch (error: any) {
      results.push({
        name: 'Auth Configuration',
        status: 'fail',
        message: 'Auth configuration check failed',
        details: error.message
      });
    }

    // 7. RLS Policies Check
    try {
      // Try to access a protected table without authentication
      const { error } = await supabase.from('users').select('*').limit(1);
      
      if (error && error.message.includes('RLS')) {
        results.push({
          name: 'Row Level Security',
          status: 'pass',
          message: 'RLS policies are active and protecting data'
        });
      } else if (error) {
        results.push({
          name: 'Row Level Security',
          status: 'warning',
          message: 'RLS policies may not be configured properly',
          details: error.message,
          fix: 'Run the RLS policies script (supabase-rls-policies.sql)'
        });
      } else {
        results.push({
          name: 'Row Level Security',
          status: 'warning',
          message: 'RLS policies may be too permissive or not configured',
          fix: 'Review and apply RLS policies for data security'
        });
      }
    } catch (error: any) {
      results.push({
        name: 'Row Level Security',
        status: 'fail',
        message: 'Error checking RLS policies',
        details: error.message
      });
    }

    // Calculate summary
    const summary = results.reduce(
      (acc, result) => {
        acc[result.status]++;
        return acc;
      },
      { pass: 0, fail: 0, warning: 0 }
    );

    setChecks(results);
    setSummary(summary);
    setIsRunning(false);
  };

  useEffect(() => {
    runSystemCheck();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'fail':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'border-green-200 bg-green-50';
      case 'fail':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">System Health Check</h1>
          <p className="text-gray-600">Comprehensive check of Supabase connection, database, and authentication</p>
        </div>

        {/* Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{summary.pass}</div>
              <div className="text-sm text-gray-600">Passing</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{summary.fail}</div>
              <div className="text-sm text-gray-600">Failing</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600">{summary.warning}</div>
              <div className="text-sm text-gray-600">Warnings</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Button onClick={runSystemCheck} disabled={isRunning} className="w-full">
                {isRunning ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4 mr-2" />
                )}
                Re-run Check
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Results */}
        <div className="space-y-4">
          {checks.map((check, index) => (
            <Card key={index} className={getStatusColor(check.status)}>
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  {getStatusIcon(check.status)}
                  <div className="flex-1">
                    <h3 className="font-medium mb-1">{check.name}</h3>
                    <p className="text-sm text-gray-700 mb-2">{check.message}</p>
                    
                    {check.details && (
                      <details className="mb-2">
                        <summary className="cursor-pointer text-xs text-gray-600">View Details</summary>
                        <pre className="bg-white p-2 rounded text-xs mt-1 overflow-auto">
                          {typeof check.details === 'string' ? check.details : JSON.stringify(check.details, null, 2)}
                        </pre>
                      </details>
                    )}
                    
                    {check.fix && (
                      <div className="bg-blue-50 border border-blue-200 rounded p-2 mt-2">
                        <p className="text-xs text-blue-800">
                          <strong>Fix:</strong> {check.fix}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        {summary.fail > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-red-500" />
                Critical Issues Found
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="font-medium text-red-800 mb-2">Immediate Actions Required:</h4>
                <ul className="text-sm text-red-700 space-y-1">
                  <li>• If database tables are missing: Run database setup scripts in Supabase SQL Editor</li>
                  <li>• If environment variables are wrong: Check your .env.local file</li>
                  <li>• If Supabase connection fails: Verify your project is active and credentials are correct</li>
                  <li>• If authentication fails: Check Supabase auth settings and site URL</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
