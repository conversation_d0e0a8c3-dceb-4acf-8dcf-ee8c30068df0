'use client';

import { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/components/auth/auth-provider';
import { ResumeEditor } from '@/components/resume-builder/resume-editor';
import { ResumePreview } from '@/components/resume-builder/resume-preview';
import { TemplateSelector } from '@/components/resume-builder/template-selector';
import { ResumeScoreCard } from '@/components/ai-optimization/resume-score-card';
import { JobMatchOptimizer } from '@/components/ai-optimization/job-match-optimizer';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { 
  FileText, 
  Save, 
  Download, 
  Eye, 
  EyeOff,
  ArrowLeft,
  Settings
} from 'lucide-react';
import { ResumeContent, ResumeTemplate } from '@/types/resume';
import { supabase } from '@/lib/supabase/client';
import toast from 'react-hot-toast';
import Link from 'next/link';

const defaultResumeContent: ResumeContent = {
  personalInfo: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    location: '',
    website: '',
    linkedin: '',
    github: '',
    summary: '',
  },
  experience: [],
  education: [],
  skills: [],
  projects: [],
  certifications: [],
  languages: [],
  customSections: [],
};

export default function ResumeBuilderPage() {
  return (
    <ProtectedRoute>
      <ResumeBuilderContent />
    </ProtectedRoute>
  );
}

function ResumeBuilderContent() {
  const { user } = useAuth();
  const [resumeContent, setResumeContent] = useState<ResumeContent>(defaultResumeContent);
  const [selectedTemplate, setSelectedTemplate] = useState<ResumeTemplate | null>(null);
  const [templates, setTemplates] = useState<ResumeTemplate[]>([]);
  const [showPreview, setShowPreview] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [resumeId, setResumeId] = useState<string | null>(null);
  const [resumeTitle, setResumeTitle] = useState('Untitled Resume');

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('resume_templates')
        .select('*')
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error loading templates:', error);
        toast.error('Failed to load templates');
        return;
      }

      if (data && data.length > 0) {
        const mappedTemplates: ResumeTemplate[] = data.map(template => ({
          id: template.id,
          name: template.name,
          description: template.description || '',
          category: template.category as 'modern' | 'classic' | 'creative' | 'minimal',
          isPremium: template.is_premium,
          previewImage: template.preview_image_url || '',
          colors: template.template_data?.colors || {
            primary: '#2563eb',
            secondary: '#64748b',
            accent: '#3b82f6',
            text: '#1e293b',
            background: '#ffffff'
          },
          fonts: template.template_data?.fonts || {
            heading: 'Inter',
            body: 'Inter'
          },
          layout: template.template_data?.layout || 'two-column'
        }));
        
        setTemplates(mappedTemplates);
        // Set first template as default
        setSelectedTemplate(mappedTemplates[0]);
      }
    } catch (error) {
      console.error('Unexpected error loading templates:', error);
      toast.error('Failed to load templates');
    }
  };

  const handleSaveResume = async () => {
    if (!user || !selectedTemplate) return;

    setIsSaving(true);
    try {
      const resumeData = {
        user_id: user.id,
        title: resumeTitle,
        template_id: selectedTemplate.id,
        content: resumeContent,
        is_public: false,
      };

      let result;
      if (resumeId) {
        // Update existing resume
        result = await supabase
          .from('resumes')
          .update(resumeData)
          .eq('id', resumeId)
          .select()
          .single();
      } else {
        // Create new resume
        result = await supabase
          .from('resumes')
          .insert(resumeData)
          .select()
          .single();
      }

      if (result.error) {
        toast.error('Failed to save resume');
        console.error('Save error:', result.error);
        return;
      }

      if (result.data) {
        setResumeId(result.data.id);
        toast.success('Resume saved successfully!');
      }
    } catch (error) {
      console.error('Unexpected error saving resume:', error);
      toast.error('Failed to save resume');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDownloadPDF = () => {
    // TODO: Implement PDF download functionality
    toast.success('PDF download feature coming soon!');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <input
                    type="text"
                    value={resumeTitle}
                    onChange={(e) => setResumeTitle(e.target.value)}
                    className="text-lg font-semibold bg-transparent border-none outline-none focus:bg-gray-50 rounded px-2 py-1"
                    placeholder="Resume Title"
                  />
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? (
                  <>
                    <EyeOff className="w-4 h-4 mr-2" />
                    Hide Preview
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4 mr-2" />
                    Show Preview
                  </>
                )}
              </Button>
              
              <Button variant="outline" size="sm" onClick={handleDownloadPDF}>
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
              
              <Button size="sm" onClick={handleSaveResume} disabled={isSaving}>
                <Save className="w-4 h-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className={`grid gap-6 ${showPreview ? 'grid-cols-1 lg:grid-cols-3' : 'grid-cols-1 lg:grid-cols-2'}`}>
          {/* Editor Panel */}
          <div className="space-y-6">
            {/* Template Selector */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Template & Style
              </h3>
              <TemplateSelector
                templates={templates}
                selectedTemplate={selectedTemplate}
                onTemplateSelect={setSelectedTemplate}
              />
            </Card>

            {/* Resume Editor */}
            <ResumeEditor
              content={resumeContent}
              onContentChange={setResumeContent}
            />
          </div>

          {/* AI Optimization Panel */}
          <div className="space-y-6">
            <ResumeScoreCard
              resume={resumeContent}
              onOptimize={(suggestions) => {
                // Handle optimization suggestions
                console.log('Optimization suggestions:', suggestions);
              }}
            />

            <JobMatchOptimizer
              resume={resumeContent}
              onOptimize={(suggestions) => {
                // Handle job-specific optimization
                console.log('Job optimization suggestions:', suggestions);
              }}
            />
          </div>

          {/* Preview Panel */}
          {showPreview && (
            <div className="lg:sticky lg:top-24 lg:h-[calc(100vh-8rem)]">
              <Card className="h-full">
                <div className="p-4 border-b">
                  <h3 className="font-semibold">Preview</h3>
                </div>
                <div className="p-4 h-full overflow-auto">
                  <ResumePreview
                    content={resumeContent}
                    template={selectedTemplate}
                  />
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
