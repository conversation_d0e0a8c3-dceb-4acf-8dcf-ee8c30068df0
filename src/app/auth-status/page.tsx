'use client';

import { useEffect, useState } from 'react';
import { useSimpleAuth } from '@/components/auth/simple-auth-provider';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2, ArrowRight, RefreshCw } from 'lucide-react';

export default function AuthStatusPage() {
  const { user, session, loading } = useSimpleAuth();
  const [rawAuth, setRawAuth] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [mounted, setMounted] = useState(false);

  const checkRawAuth = async () => {
    setIsChecking(true);
    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      setRawAuth({
        session,
        user,
        sessionError,
        userError,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Auth check failed:', error);
      setRawAuth({ error: error });
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    setMounted(true);
    checkRawAuth();
  }, []);

  const goToDashboard = () => {
    window.location.href = '/dashboard';
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    window.location.href = '/auth/signin';
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading authentication status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Authentication Status</h1>
          <p className="text-gray-600">Real-time auth status without redirects</p>
        </div>

        {/* Simple Auth Provider Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {loading ? (
                <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
              ) : user ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )}
              Simple Auth Provider
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Loading:</span>
                <span className={loading ? 'text-yellow-600' : 'text-green-600'}>
                  {loading ? '⏳ Yes' : '✅ No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Has User:</span>
                <span className={user ? 'text-green-600' : 'text-red-600'}>
                  {user ? '✅ Yes' : '❌ No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Has Session:</span>
                <span className={session ? 'text-green-600' : 'text-red-600'}>
                  {session ? '✅ Yes' : '❌ No'}
                </span>
              </div>
              {user && (
                <>
                  <div className="flex justify-between">
                    <span>Email:</span>
                    <span className="text-sm">{user.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Email Confirmed:</span>
                    <span className={user.email_confirmed_at ? 'text-green-600' : 'text-red-600'}>
                      {user.email_confirmed_at ? '✅ Yes' : '❌ No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>User ID:</span>
                    <span className="text-xs font-mono">{user.id}</span>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Raw Auth Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Raw Supabase Auth
              <Button onClick={checkRawAuth} disabled={isChecking} size="sm" variant="outline">
                {isChecking ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {rawAuth ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Raw Session:</span>
                  <span className={rawAuth.session ? 'text-green-600' : 'text-red-600'}>
                    {rawAuth.session ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Raw User:</span>
                  <span className={rawAuth.user ? 'text-green-600' : 'text-red-600'}>
                    {rawAuth.user ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                {rawAuth.user && (
                  <>
                    <div className="flex justify-between">
                      <span>Raw Email:</span>
                      <span className="text-sm">{rawAuth.user.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Email Confirmed:</span>
                      <span className={rawAuth.user.email_confirmed_at ? 'text-green-600' : 'text-red-600'}>
                        {rawAuth.user.email_confirmed_at ? '✅ Yes' : '❌ No'}
                      </span>
                    </div>
                  </>
                )}
                {(rawAuth.sessionError || rawAuth.userError) && (
                  <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
                    Error: {rawAuth.sessionError?.message || rawAuth.userError?.message}
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">Loading raw auth data...</p>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {user ? (
              <>
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 text-sm font-medium">
                    ✅ You are authenticated! You should be able to access the dashboard.
                  </p>
                </div>
                
                <Button onClick={goToDashboard} className="w-full" size="lg">
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Go to Dashboard
                </Button>
                
                <Button onClick={signOut} variant="outline" className="w-full">
                  Sign Out
                </Button>
              </>
            ) : (
              <>
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 text-sm font-medium">
                    ❌ Not authenticated. You need to sign in first.
                  </p>
                </div>
                
                <Button onClick={() => window.location.href = '/quick-signin'} className="w-full">
                  Go to Quick Sign In
                </Button>
              </>
            )}

            <div className="grid grid-cols-2 gap-2">
              <Button 
                onClick={() => window.location.href = '/auth/signin'} 
                variant="ghost" 
                size="sm"
              >
                Regular Sign In
              </Button>
              <Button 
                onClick={() => window.location.href = '/signin-test'} 
                variant="ghost" 
                size="sm"
              >
                Signin Test
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Current URL Info */}
        <Card>
          <CardHeader>
            <CardTitle>Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Current URL:</span>
                <span className="font-mono text-xs">{mounted ? window.location.href : 'Loading...'}</span>
              </div>
              <div className="flex justify-between">
                <span>Pathname:</span>
                <span className="font-mono text-xs">{mounted ? window.location.pathname : 'Loading...'}</span>
              </div>
              <div className="flex justify-between">
                <span>Timestamp:</span>
                <span className="text-xs">{new Date().toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
