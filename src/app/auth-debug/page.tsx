'use client';

import { useEffect, useState } from 'react';
import { useSimpleAuth } from '@/components/auth/simple-auth-provider';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Loader2, ArrowRight, RefreshCw } from 'lucide-react';

export default function AuthDebugPage() {
  const { user, session, loading } = useSimpleAuth();
  const [mounted, setMounted] = useState(false);
  const [authLogs, setAuthLogs] = useState<string[]>([]);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [rawAuthData, setRawAuthData] = useState<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setAuthLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[DEBUG AUTH] ${message}`);
  };

  useEffect(() => {
    setMounted(true);
    addLog('Component mounted');
    
    // Check initial auth state
    checkAuthState();
    
    // Listen to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      addLog(`Auth state change: ${event} - User: ${session?.user?.email || 'None'}`);
      checkAuthState();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const checkAuthState = async () => {
    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      setRawAuthData({
        session,
        user,
        sessionError,
        userError,
        timestamp: new Date().toISOString()
      });

      addLog(`Session check - Session: ${session ? 'EXISTS' : 'NULL'}, User: ${user ? user.email : 'NULL'}`);
      
      if (sessionError) addLog(`Session Error: ${sessionError.message}`);
      if (userError) addLog(`User Error: ${userError.message}`);
      
    } catch (error: any) {
      addLog(`Auth check failed: ${error.message}`);
    }
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSigningIn(true);
    addLog(`Starting sign in for: ${email}`);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password,
      });

      if (error) {
        addLog(`Sign in error: ${error.message}`);
        setIsSigningIn(false);
        return;
      }

      if (data.user) {
        addLog(`Sign in successful! User: ${data.user.email}`);
        addLog(`Session: ${data.session ? 'Created' : 'Not created'}`);
        
        // Check auth state after sign in
        setTimeout(() => {
          checkAuthState();
          addLog('Checking auth state after sign in...');
        }, 1000);
        
        // Try manual redirect after a delay
        setTimeout(() => {
          addLog('Attempting manual redirect to dashboard...');
          window.location.href = '/dashboard';
        }, 2000);
      }
    } catch (error: any) {
      addLog(`Unexpected error: ${error.message}`);
    } finally {
      setIsSigningIn(false);
    }
  };

  const clearLogs = () => {
    setAuthLogs([]);
  };

  const testDirectDashboard = () => {
    addLog('Testing direct dashboard access...');
    window.location.href = '/dashboard';
  };

  const signOut = async () => {
    addLog('Signing out...');
    await supabase.auth.signOut();
    addLog('Sign out complete');
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading debug page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">🔍 Authentication Debug Center</h1>
          <p className="text-gray-600">Comprehensive auth state debugging and testing</p>
        </div>

        {/* Current Auth Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {loading ? (
                <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
              ) : user ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )}
              Current Auth Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Provider Loading:</span>
                  <span className={loading ? 'text-yellow-600' : 'text-green-600'}>
                    {loading ? '⏳ Yes' : '✅ No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Provider User:</span>
                  <span className={user ? 'text-green-600' : 'text-red-600'}>
                    {user ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Provider Session:</span>
                  <span className={session ? 'text-green-600' : 'text-red-600'}>
                    {session ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Raw User:</span>
                  <span className={rawAuthData?.user ? 'text-green-600' : 'text-red-600'}>
                    {rawAuthData?.user ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Raw Session:</span>
                  <span className={rawAuthData?.session ? 'text-green-600' : 'text-red-600'}>
                    {rawAuthData?.session ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Mounted:</span>
                  <span className="text-green-600">✅ Yes</span>
                </div>
              </div>
            </div>

            {user && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">✅ User Details:</h4>
                <div className="text-sm text-green-700 space-y-1">
                  <div>Email: {user.email}</div>
                  <div>ID: {user.id}</div>
                  <div>Email Confirmed: {user.email_confirmed_at ? '✅ Yes' : '❌ No'}</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sign In Test */}
        <Card>
          <CardHeader>
            <CardTitle>🔐 Sign In Test</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                    disabled={isSigningIn}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    disabled={isSigningIn}
                  />
                </div>
              </div>
              
              <div className="flex gap-3">
                <Button type="submit" disabled={isSigningIn} className="flex-1">
                  {isSigningIn ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Signing In...
                    </>
                  ) : (
                    <>
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Debug Sign In
                    </>
                  )}
                </Button>
                
                <Button type="button" onClick={checkAuthState} variant="outline">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Check Auth
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>🚀 Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <Button onClick={testDirectDashboard} variant="outline">
                Test Dashboard Access
              </Button>
              <Button onClick={() => window.location.href = '/auth-status'} variant="outline">
                Go to Auth Status
              </Button>
              <Button onClick={() => window.location.href = '/'} variant="outline">
                Go to Home
              </Button>
              <Button onClick={signOut} variant="destructive">
                Sign Out
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Debug Logs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              📝 Debug Logs
              <Button onClick={clearLogs} size="sm" variant="outline">
                Clear Logs
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
              {authLogs.length === 0 ? (
                <div className="text-gray-500">No logs yet...</div>
              ) : (
                authLogs.map((log, index) => (
                  <div key={index} className="mb-1">{log}</div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
