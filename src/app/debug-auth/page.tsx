'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';
import toast from 'react-hot-toast';

export default function DebugAuthPage() {
  const [authState, setAuthState] = useState<any>(null);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuthStatus = async () => {
    setIsLoading(true);
    try {
      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      // Get user profile from public.users
      let profile = null;
      if (user) {
        const { data: profileData, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (!profileError) {
          profile = profileData;
        }
      }

      setAuthState({
        session,
        user,
        sessionError,
        userError,
        hasSession: !!session,
        hasUser: !!user,
        isEmailConfirmed: !!user?.email_confirmed_at,
        email: user?.email,
        emailConfirmedAt: user?.email_confirmed_at,
        createdAt: user?.created_at,
        lastSignInAt: user?.last_sign_in_at,
        userMetadata: user?.user_metadata,
      });

      setUserProfile(profile);
    } catch (error) {
      console.error('Auth check error:', error);
      toast.error('Failed to check auth status');
    } finally {
      setIsLoading(false);
    }
  };

  const forceEmailConfirmation = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        toast.error('No user found');
        return;
      }

      // This is a workaround - sign out and back in to refresh the session
      await supabase.auth.signOut();
      toast.success('Signed out. Please sign in again to refresh your session.');
      
      // Redirect to sign in
      window.location.href = '/auth/signin';
    } catch (error) {
      console.error('Force confirmation error:', error);
      toast.error('Failed to refresh session');
    }
  };

  const createUserProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        toast.error('No user found');
        return;
      }

      const { error } = await supabase
        .from('users')
        .insert({
          id: user.id,
          email: user.email!,
          full_name: user.user_metadata?.full_name || '',
        });

      if (error) {
        throw error;
      }

      toast.success('User profile created successfully');
      checkAuthStatus();
    } catch (error: any) {
      console.error('Create profile error:', error);
      toast.error(error.message || 'Failed to create user profile');
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Checking authentication status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Authentication Debug</h1>
          <p className="text-gray-600">Check your current authentication status</p>
        </div>

        {/* Auth Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {authState?.hasUser ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )}
              Authentication Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Has Session:</strong> {authState?.hasSession ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Has User:</strong> {authState?.hasUser ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Email Confirmed:</strong> {authState?.isEmailConfirmed ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Email:</strong> {authState?.email || 'N/A'}
              </div>
            </div>

            {authState?.emailConfirmedAt && (
              <div>
                <strong>Email Confirmed At:</strong> {new Date(authState.emailConfirmedAt).toLocaleString()}
              </div>
            )}

            {authState?.createdAt && (
              <div>
                <strong>Account Created:</strong> {new Date(authState.createdAt).toLocaleString()}
              </div>
            )}

            {authState?.lastSignInAt && (
              <div>
                <strong>Last Sign In:</strong> {new Date(authState.lastSignInAt).toLocaleString()}
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Profile Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {userProfile ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-yellow-500" />
              )}
              User Profile Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {userProfile ? (
              <div className="space-y-2">
                <div><strong>Profile ID:</strong> {userProfile.id}</div>
                <div><strong>Email:</strong> {userProfile.email}</div>
                <div><strong>Full Name:</strong> {userProfile.full_name || 'Not set'}</div>
                <div><strong>Profile Created:</strong> {new Date(userProfile.created_at).toLocaleString()}</div>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-yellow-600">No user profile found in database</p>
                <Button onClick={createUserProfile}>
                  Create User Profile
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Debug Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Button onClick={checkAuthStatus} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh Status
              </Button>
              
              {authState?.hasUser && !authState?.isEmailConfirmed && (
                <Button onClick={forceEmailConfirmation} variant="outline">
                  Force Session Refresh
                </Button>
              )}
            </div>

            {authState?.hasUser && !authState?.isEmailConfirmed && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">Email Not Confirmed</h4>
                <p className="text-sm text-yellow-700">
                  Your email appears to be unconfirmed. If you clicked the confirmation link, 
                  try signing out and back in to refresh your session.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Raw Data */}
        <Card>
          <CardHeader>
            <CardTitle>Raw Auth Data</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto">
              {JSON.stringify({ authState, userProfile }, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
