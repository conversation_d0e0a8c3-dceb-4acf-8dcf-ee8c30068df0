'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

export default function HydrationTestPage() {
  const [mounted, setMounted] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  const [currentPath, setCurrentPath] = useState('');

  useEffect(() => {
    setMounted(true);
    setCurrentUrl(window.location.href);
    setCurrentPath(window.location.pathname);
  }, []);

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Hydration Test</h1>
          <p className="text-gray-600">Testing hydration-safe rendering</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              Hydration Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 text-sm font-medium">
                  ✅ Page mounted successfully without hydration errors!
                </p>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Mounted:</span>
                  <span className="text-green-600">✅ Yes</span>
                </div>
                <div className="flex justify-between">
                  <span>Current URL:</span>
                  <span className="font-mono text-xs break-all">{currentUrl}</span>
                </div>
                <div className="flex justify-between">
                  <span>Current Path:</span>
                  <span className="font-mono text-xs">{currentPath}</span>
                </div>
                <div className="flex justify-between">
                  <span>Timestamp:</span>
                  <span className="text-xs">{new Date().toLocaleString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Navigation Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-gray-600 mb-4">
              These buttons should work without hydration issues:
            </p>
            
            <div className="grid grid-cols-2 gap-3">
              <Button 
                onClick={() => window.location.href = '/'} 
                variant="outline"
              >
                Home
              </Button>
              <Button 
                onClick={() => window.location.href = '/auth-status'} 
                variant="outline"
              >
                Auth Status
              </Button>
              <Button 
                onClick={() => window.location.href = '/quick-signin'} 
                variant="outline"
              >
                Quick Sign In
              </Button>
              <Button 
                onClick={() => window.location.href = '/dashboard'} 
                variant="outline"
              >
                Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Hydration Best Practices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">✅ What We Fixed:</h4>
                <ul className="text-blue-700 space-y-1">
                  <li>• Added `mounted` state to prevent SSR/client mismatch</li>
                  <li>• Used `useEffect` to safely access `window` object</li>
                  <li>• Added loading state during hydration</li>
                  <li>• Prevented rendering until client-side mount</li>
                </ul>
              </div>

              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">⚠️ Common Hydration Issues:</h4>
                <ul className="text-yellow-700 space-y-1">
                  <li>• Accessing `window` during server-side rendering</li>
                  <li>• Different content between server and client</li>
                  <li>• Date/time differences between server and client</li>
                  <li>• Random values that differ between renders</li>
                </ul>
              </div>

              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">✅ Solution Pattern:</h4>
                <pre className="text-green-700 text-xs bg-white p-2 rounded mt-2 overflow-auto">
{`const [mounted, setMounted] = useState(false);

useEffect(() => {
  setMounted(true);
}, []);

if (!mounted) {
  return <LoadingComponent />;
}

// Safe to use window object here`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
