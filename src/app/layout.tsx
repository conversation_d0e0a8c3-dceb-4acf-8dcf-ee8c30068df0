import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/components/auth/auth-provider";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "CVLeap - Professional Resume Builder & Job Search Platform",
  description: "Create stunning resumes and automate your job search with CVLeap. Build professional resumes, track applications, and find your dream job faster.",
  keywords: ["resume builder", "job search", "career", "CV", "application tracking"],
  authors: [{ name: "CVLeap Team" }],
  creator: "CVLeap",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://cvleap.com",
    title: "CVLeap - Professional Resume Builder & Job Search Platform",
    description: "Create stunning resumes and automate your job search with <PERSON><PERSON><PERSON>eap",
    siteName: "CVLeap",
  },
  twitter: {
    card: "summary_large_image",
    title: "CVLeap - Professional Resume Builder & Job Search Platform",
    description: "Create stunning resumes and automate your job search with CVLeap",
    creator: "@cvleap",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: "hsl(var(--card))",
                color: "hsl(var(--card-foreground))",
                border: "1px solid hsl(var(--border))",
              },
            }}
          />
        </AuthProvider>
      </body>
    </html>
  );
}
