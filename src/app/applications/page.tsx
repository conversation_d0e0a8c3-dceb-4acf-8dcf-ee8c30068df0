'use client';

import { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/components/auth/auth-provider';
import { ApplicationsList } from '@/components/applications/applications-list';
import { ApplicationStats } from '@/components/applications/application-stats';
import { ApplicationFilters } from '@/components/applications/application-filters';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft,
  Search,
  Filter,
  Plus,
  Download,
  Calendar,
  BarChart3
} from 'lucide-react';
import { JobApplication, ApplicationStatus } from '@/types/job';
import { supabase } from '@/lib/supabase/client';
import toast from 'react-hot-toast';
import Link from 'next/link';

export default function ApplicationsPage() {
  return (
    <ProtectedRoute>
      <ApplicationsContent />
    </ProtectedRoute>
  );
}

function ApplicationsContent() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<JobApplication[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | 'all'>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    if (user) {
      loadApplications();
    }
  }, [user]);

  useEffect(() => {
    filterApplications();
  }, [applications, searchQuery, statusFilter, dateFilter]);

  const loadApplications = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('job_applications')
        .select('*')
        .eq('user_id', user.id)
        .order('applied_at', { ascending: false });

      if (error) {
        throw error;
      }

      setApplications(data || []);
    } catch (error) {
      console.error('Failed to load applications:', error);
      toast.error('Failed to load applications');
    } finally {
      setIsLoading(false);
    }
  };

  const filterApplications = () => {
    let filtered = [...applications];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(app =>
        app.job_title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.company_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === statusFilter);
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case '7days':
          filterDate.setDate(now.getDate() - 7);
          break;
        case '30days':
          filterDate.setDate(now.getDate() - 30);
          break;
        case '90days':
          filterDate.setDate(now.getDate() - 90);
          break;
        default:
          filterDate.setFullYear(1970);
      }

      filtered = filtered.filter(app => 
        new Date(app.applied_at) >= filterDate
      );
    }

    setFilteredApplications(filtered);
  };

  const handleStatusUpdate = async (applicationId: string, newStatus: ApplicationStatus) => {
    try {
      const { error } = await supabase
        .from('job_applications')
        .update({ 
          status: newStatus,
          response_at: newStatus !== 'applied' ? new Date().toISOString() : null
        })
        .eq('id', applicationId);

      if (error) {
        throw error;
      }

      // Update local state
      setApplications(applications.map(app =>
        app.id === applicationId 
          ? { ...app, status: newStatus, response_at: newStatus !== 'applied' ? new Date().toISOString() : null }
          : app
      ));

      toast.success('Application status updated');
    } catch (error) {
      console.error('Failed to update application status:', error);
      toast.error('Failed to update status');
    }
  };

  const handleAddNote = async (applicationId: string, note: string) => {
    try {
      const { error } = await supabase
        .from('job_applications')
        .update({ notes: note })
        .eq('id', applicationId);

      if (error) {
        throw error;
      }

      // Update local state
      setApplications(applications.map(app =>
        app.id === applicationId ? { ...app, notes: note } : app
      ));

      toast.success('Note added');
    } catch (error) {
      console.error('Failed to add note:', error);
      toast.error('Failed to add note');
    }
  };

  const exportApplications = () => {
    // Create CSV content
    const headers = ['Company', 'Position', 'Status', 'Applied Date', 'Response Date', 'Notes'];
    const csvContent = [
      headers.join(','),
      ...filteredApplications.map(app => [
        `"${app.company_name}"`,
        `"${app.job_title}"`,
        app.status,
        new Date(app.applied_at).toLocaleDateString(),
        app.response_at ? new Date(app.response_at).toLocaleDateString() : '',
        `"${app.notes || ''}"`
      ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `job-applications-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast.success('Applications exported to CSV');
  };

  const getStats = () => {
    const total = applications.length;
    const pending = applications.filter(app => app.status === 'applied').length;
    const interviews = applications.filter(app => ['screening', 'interview'].includes(app.status)).length;
    const offers = applications.filter(app => app.status === 'offer').length;
    const rejected = applications.filter(app => app.status === 'rejected').length;

    return { total, pending, interviews, offers, rejected };
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <h1 className="text-xl font-semibold">Job Applications</h1>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button variant="outline" onClick={exportApplications}>
                <Download className="w-4 h-4 mr-2" />
                Export CSV
              </Button>
              <Link href="/job-search">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Find Jobs
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats */}
        <ApplicationStats stats={getStats()} />

        {/* Search and Filters */}
        <Card className="p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by company or position..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as ApplicationStatus | 'all')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">All Statuses</option>
                <option value="applied">Applied</option>
                <option value="viewed">Viewed</option>
                <option value="screening">Screening</option>
                <option value="interview">Interview</option>
                <option value="offer">Offer</option>
                <option value="rejected">Rejected</option>
                <option value="withdrawn">Withdrawn</option>
              </select>
              
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">All Time</option>
                <option value="7days">Last 7 days</option>
                <option value="30days">Last 30 days</option>
                <option value="90days">Last 90 days</option>
              </select>
              
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>
        </Card>

        {/* Applications List */}
        <ApplicationsList
          applications={filteredApplications}
          onStatusUpdate={handleStatusUpdate}
          onAddNote={handleAddNote}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
