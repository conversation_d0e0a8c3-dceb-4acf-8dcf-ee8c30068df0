'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FileText,
  Search,
  Target,
  BarChart3,
  Plus,
  LogOut,
  User,
  Loader2
} from 'lucide-react';
import Link from 'next/link';

export default function DashboardDirectPage() {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('Auth check error:', error);
        // Redirect to sign in if no user
        window.location.href = '/simple-signin';
        return;
      }

      if (!user) {
        console.log('No user found, redirecting to sign in');
        window.location.href = '/simple-signin';
        return;
      }

      console.log('User found:', user.email);
      setUser(user);
    } catch (error) {
      console.error('Auth check failed:', error);
      window.location.href = '/simple-signin';
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      window.location.href = '/';
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  if (!mounted || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center animate-pulse">
              <FileText className="w-7 h-7 text-white" />
            </div>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Loading Dashboard...
          </h1>
          <p className="text-gray-600">
            Please wait while we load your account.
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in checkAuth
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                CVLeap
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-700">
                  {user?.user_metadata?.full_name || user?.email}
                </span>
              </div>
              <Button onClick={handleSignOut} variant="outline" size="sm">
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.user_metadata?.full_name?.split(' ')[0] || 'there'}!
          </h1>
          <p className="text-gray-600">
            Ready to take your career to the next level? Let's get started.
          </p>
        </div>

        {/* Success Message */}
        <div className="mb-8 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs">✓</span>
            </div>
            <div>
              <h3 className="font-medium text-green-800">🎉 Dashboard Loading Successfully!</h3>
              <p className="text-green-700 text-sm">You've successfully signed in and accessed the dashboard without redirect loops.</p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              <CardTitle className="text-lg">Create Resume</CardTitle>
              <CardDescription>Build a professional resume</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/resume-builder">
                <Button className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Get Started
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
                <Search className="w-5 h-5 text-purple-600" />
              </div>
              <CardTitle className="text-lg">Find Jobs</CardTitle>
              <CardDescription>Search for opportunities</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/job-search">
                <Button variant="outline" className="w-full">
                  <Search className="w-4 h-4 mr-2" />
                  Search Jobs
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-2">
                <Target className="w-5 h-5 text-green-600" />
              </div>
              <CardTitle className="text-lg">Auto Apply</CardTitle>
              <CardDescription>Automate applications</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/auto-apply">
                <Button variant="outline" className="w-full">
                  <Target className="w-4 h-4 mr-2" />
                  Set Up
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mb-2">
                <BarChart3 className="w-5 h-5 text-orange-600" />
              </div>
              <CardTitle className="text-lg">Track Progress</CardTitle>
              <CardDescription>Monitor applications</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/applications">
                <Button variant="outline" className="w-full">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  View Stats
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest actions and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="w-4 h-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Welcome to CVLeap!</p>
                  <p className="text-xs text-gray-500">Get started by creating your first resume</p>
                </div>
                <span className="text-xs text-gray-400">Just now</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Debug Info */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>🔧 Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">User Email:</span>
                <span className="ml-2">{user.email}</span>
              </div>
              <div>
                <span className="font-medium">User ID:</span>
                <span className="ml-2 font-mono text-xs">{user.id}</span>
              </div>
              <div>
                <span className="font-medium">Email Confirmed:</span>
                <span className="ml-2">{user.email_confirmed_at ? '✅ Yes' : '❌ No'}</span>
              </div>
              <div>
                <span className="font-medium">Auth Method:</span>
                <span className="ml-2">Direct Supabase</span>
              </div>
            </div>
            
            <div className="mt-4 flex gap-2">
              <Button onClick={() => window.location.href = '/test-center'} variant="outline" size="sm">
                🧪 Test Center
              </Button>
              <Button onClick={() => window.location.href = '/auth-debug'} variant="outline" size="sm">
                🔍 Auth Debug
              </Button>
              <Button onClick={() => window.location.href = '/dashboard'} variant="outline" size="sm">
                📊 Original Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
