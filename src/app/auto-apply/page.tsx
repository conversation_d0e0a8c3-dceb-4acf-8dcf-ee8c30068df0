'use client';

import { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/components/auth/auth-provider';
import { AutoApplyStats } from '@/components/auto-apply/auto-apply-stats';
import { SearchLoopsList } from '@/components/auto-apply/search-loops-list';
import { CreateSearchLoop } from '@/components/auto-apply/create-search-loop';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ArrowLeft,
  Plus,
  Play,
  Pause,
  Settings,
  Target,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react';
import { JobSearchLoop } from '@/types/job';
import { AutoApplyService } from '@/lib/auto-apply/auto-apply-service';
import { supabase } from '@/lib/supabase/client';
import toast from 'react-hot-toast';
import Link from 'next/link';

export default function AutoApplyPage() {
  return (
    <ProtectedRoute>
      <AutoApplyContent />
    </ProtectedRoute>
  );
}

function AutoApplyContent() {
  const { user } = useAuth();
  const [searchLoops, setSearchLoops] = useState<JobSearchLoop[]>([]);
  const [autoApplyStats, setAutoApplyStats] = useState({
    applicationsToday: 0,
    activeLoops: 0,
    totalApplications: 0,
    successRate: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateLoop, setShowCreateLoop] = useState(false);

  const autoApplyService = new AutoApplyService();

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const [loops, stats] = await Promise.all([
        loadSearchLoops(),
        autoApplyService.getAutoApplyStatus(user.id)
      ]);
      
      setSearchLoops(loops);
      setAutoApplyStats(stats);
    } catch (error) {
      console.error('Failed to load auto-apply data:', error);
      toast.error('Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSearchLoops = async (): Promise<JobSearchLoop[]> => {
    if (!user) return [];

    const { data, error } = await supabase
      .from('job_search_loops')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Failed to load search loops:', error);
      return [];
    }

    return data || [];
  };

  const handleCreateLoop = async (loopData: Partial<JobSearchLoop>) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('job_search_loops')
        .insert({
          user_id: user.id,
          name: loopData.name!,
          search_filters: loopData.search_filters!,
          is_active: loopData.is_active || false,
          auto_apply: loopData.auto_apply || false,
          email_template: loopData.email_template,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      setSearchLoops([data, ...searchLoops]);
      setShowCreateLoop(false);
      toast.success('Search loop created successfully!');
    } catch (error) {
      console.error('Failed to create search loop:', error);
      toast.error('Failed to create search loop');
    }
  };

  const handleToggleLoop = async (loopId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('job_search_loops')
        .update({ is_active: isActive })
        .eq('id', loopId);

      if (error) {
        throw error;
      }

      setSearchLoops(searchLoops.map(loop => 
        loop.id === loopId ? { ...loop, is_active: isActive } : loop
      ));

      toast.success(`Search loop ${isActive ? 'activated' : 'paused'}`);
    } catch (error) {
      console.error('Failed to toggle search loop:', error);
      toast.error('Failed to update search loop');
    }
  };

  const handleRunLoop = async (loop: JobSearchLoop) => {
    if (!user) return;

    try {
      toast.loading('Running auto-apply...', { id: 'auto-apply' });

      // This would typically be handled by a background job
      // For demo purposes, we'll simulate it here
      const config = {
        userId: user.id,
        searchLoop: loop,
        resumeId: 'default-resume-id', // Would get from user's default resume
        coverLetterTemplate: loop.email_template || 'Default cover letter template',
        maxApplicationsPerDay: 10,
        excludeAppliedJobs: true
      };

      const results = await autoApplyService.runAutoApply(config);
      const successCount = results.filter(r => r.success).length;

      toast.success(
        `Applied to ${successCount} out of ${results.length} jobs`,
        { id: 'auto-apply' }
      );

      // Refresh data
      loadData();
    } catch (error) {
      console.error('Failed to run auto-apply:', error);
      toast.error('Failed to run auto-apply', { id: 'auto-apply' });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading auto-apply dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <h1 className="text-xl font-semibold">Auto-Apply</h1>
            </div>
            <Button onClick={() => setShowCreateLoop(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Search Loop
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Applications Today</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{autoApplyStats.applicationsToday}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Loops</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{autoApplyStats.activeLoops}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{autoApplyStats.totalApplications}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{autoApplyStats.successRate.toFixed(1)}%</div>
            </CardContent>
          </Card>
        </div>

        {/* Search Loops */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Search Loops</h2>
          </div>

          {searchLoops.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No search loops yet
                </h3>
                <p className="text-gray-600 mb-4">
                  Create your first search loop to start automatically applying to jobs.
                </p>
                <Button onClick={() => setShowCreateLoop(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Search Loop
                </Button>
              </CardContent>
            </Card>
          ) : (
            <SearchLoopsList
              searchLoops={searchLoops}
              onToggleLoop={handleToggleLoop}
              onRunLoop={handleRunLoop}
            />
          )}
        </div>
      </div>

      {/* Create Search Loop Modal */}
      {showCreateLoop && (
        <CreateSearchLoop
          onClose={() => setShowCreateLoop(false)}
          onCreate={handleCreateLoop}
        />
      )}
    </div>
  );
}
