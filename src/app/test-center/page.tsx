'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2, ArrowRight, Bug, Zap, Shield, Settings } from 'lucide-react';

export default function TestCenterPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading test center...</p>
        </div>
      </div>
    );
  }

  const testPages = [
    {
      title: "🔐 Simple Sign In",
      description: "Direct authentication without complex redirects",
      url: "/simple-signin",
      icon: <Zap className="w-5 h-5" />,
      status: "recommended",
      features: ["Direct Supabase auth", "Clean redirects", "No provider conflicts"]
    },
    {
      title: "🔍 Auth Debug",
      description: "Comprehensive authentication debugging",
      url: "/auth-debug",
      icon: <Bug className="w-5 h-5" />,
      status: "debug",
      features: ["Real-time auth logs", "State inspection", "Manual testing"]
    },
    {
      title: "📊 Auth Status",
      description: "Check current authentication status",
      url: "/auth-status",
      icon: <Shield className="w-5 h-5" />,
      status: "stable",
      features: ["Provider status", "Raw auth data", "Hydration safe"]
    },
    {
      title: "⚡ Quick Sign In",
      description: "Original quick signin implementation",
      url: "/quick-signin",
      icon: <ArrowRight className="w-5 h-5" />,
      status: "legacy",
      features: ["Provider integration", "Auto redirects", "May have conflicts"]
    },
    {
      title: "🔧 System Check",
      description: "Overall system health and connectivity",
      url: "/system-check",
      icon: <Settings className="w-5 h-5" />,
      status: "utility",
      features: ["Supabase connection", "Environment check", "API status"]
    },
    {
      title: "🧪 Hydration Test",
      description: "Test hydration-safe rendering",
      url: "/hydration-test",
      icon: <CheckCircle className="w-5 h-5" />,
      status: "utility",
      features: ["SSR/Client sync", "Window access", "Mount detection"]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'recommended': return 'bg-green-100 text-green-800 border-green-200';
      case 'debug': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'stable': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'legacy': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'utility': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">🧪 CVLeap Test Center</h1>
          <p className="text-gray-600">Comprehensive testing and debugging tools for authentication and app functionality</p>
        </div>

        {/* Status Overview */}
        <Card>
          <CardHeader>
            <CardTitle>🎯 Current Issue: Sign In Redirect Loop</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="font-medium text-red-800 mb-2">❌ Problem:</h4>
                <p className="text-red-700 text-sm">After successful sign in, users are redirected back to the sign in page instead of the dashboard.</p>
              </div>
              
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">🔍 Debugging Steps:</h4>
                <ol className="text-blue-700 text-sm space-y-1 list-decimal list-inside">
                  <li>Use <strong>Simple Sign In</strong> for direct authentication</li>
                  <li>Check <strong>Auth Debug</strong> for real-time auth state</li>
                  <li>Verify <strong>Auth Status</strong> after sign in</li>
                  <li>Test direct dashboard access</li>
                </ol>
              </div>

              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">✅ Solutions Applied:</h4>
                <ul className="text-green-700 text-sm space-y-1 list-disc list-inside">
                  <li>Disabled automatic redirects in SimpleAuthProvider</li>
                  <li>Created direct sign in without provider conflicts</li>
                  <li>Fixed hydration issues</li>
                  <li>Added comprehensive debugging tools</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Pages Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testPages.map((page, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  {page.icon}
                  {page.title}
                </CardTitle>
                <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(page.status)}`}>
                  {page.status.toUpperCase()}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600 text-sm">{page.description}</p>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Features:</h4>
                  <ul className="text-xs text-gray-600 space-y-1">
                    {page.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Button 
                  onClick={() => window.location.href = page.url}
                  className="w-full"
                  variant={page.status === 'recommended' ? 'default' : 'outline'}
                >
                  Open Test Page
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>🚀 Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button onClick={() => window.location.href = '/'} variant="outline">
                🏠 Home
              </Button>
              <Button onClick={() => window.location.href = '/dashboard-direct'} variant="outline">
                📊 Dashboard (Direct)
              </Button>
              <Button onClick={() => window.location.href = '/auth/signin'} variant="outline">
                🔐 Regular Sign In
              </Button>
              <Button onClick={() => window.location.href = '/auth/signup'} variant="outline">
                📝 Sign Up
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recommended Testing Flow */}
        <Card>
          <CardHeader>
            <CardTitle>📋 Recommended Testing Flow</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                <div>
                  <h4 className="font-medium">Start with Simple Sign In</h4>
                  <p className="text-sm text-gray-600">Use the direct authentication method to bypass provider conflicts</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <div>
                  <h4 className="font-medium">Monitor with Auth Debug</h4>
                  <p className="text-sm text-gray-600">Watch real-time auth state changes and logs</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <div>
                  <h4 className="font-medium">Verify with Auth Status</h4>
                  <p className="text-sm text-gray-600">Check that authentication persisted correctly</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                <div>
                  <h4 className="font-medium">Test Dashboard Access</h4>
                  <p className="text-sm text-gray-600">Confirm you can access protected routes</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
