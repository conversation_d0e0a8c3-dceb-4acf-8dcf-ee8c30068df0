'use client';

import { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { JobSearchFilters } from '@/components/job-search/job-search-filters';
import { JobList } from '@/components/job-search/job-list';
import { JobDetails } from '@/components/job-search/job-details';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Filter, 
  MapPin, 
  Briefcase,
  ArrowLeft,
  Bookmark,
  BookmarkCheck
} from 'lucide-react';
import { Job, JobSearchFilters as JobFilters } from '@/types/job';
import { JobAggregator } from '@/lib/job-apis/job-aggregator';
import toast from 'react-hot-toast';
import Link from 'next/link';

const defaultFilters: JobFilters = {
  keywords: [],
  location: '',
  radius: 25,
  jobType: [],
  experienceLevel: [],
  salaryMin: undefined,
  salaryMax: undefined,
  datePosted: 'any',
  remote: false,
  companies: [],
  excludeCompanies: [],
};

export default function JobSearchPage() {
  return (
    <ProtectedRoute>
      <JobSearchContent />
    </ProtectedRoute>
  );
}

function JobSearchContent() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [filters, setFilters] = useState<JobFilters>(defaultFilters);
  const [searchQuery, setSearchQuery] = useState('');
  const [locationQuery, setLocationQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [savedJobs, setSavedJobs] = useState<Set<string>>(new Set());

  const jobAggregator = new JobAggregator();

  useEffect(() => {
    handleSearch();
  }, []);

  const handleSearch = async () => {
    setIsLoading(true);
    try {
      const searchFilters: JobFilters = {
        ...filters,
        keywords: searchQuery ? [searchQuery] : [],
        location: locationQuery,
      };

      const results = await jobAggregator.searchJobs(searchFilters);
      setJobs(results);
      
      if (results.length === 0) {
        toast.error('No jobs found matching your criteria');
      } else {
        toast.success(`Found ${results.length} jobs`);
      }
    } catch (error) {
      toast.error('Failed to search jobs');
      console.error('Job search error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (newFilters: JobFilters) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    handleSearch();
    setShowFilters(false);
  };

  const toggleSaveJob = (jobId: string) => {
    const newSavedJobs = new Set(savedJobs);
    if (newSavedJobs.has(jobId)) {
      newSavedJobs.delete(jobId);
      toast.success('Job removed from saved');
    } else {
      newSavedJobs.add(jobId);
      toast.success('Job saved');
    }
    setSavedJobs(newSavedJobs);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <h1 className="text-xl font-semibold">Job Search</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Search Bar */}
        <Card className="p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Job title, keywords, or company"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <div className="flex-1">
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Location"
                  value={locationQuery}
                  onChange={(e) => setLocationQuery(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
              <Button onClick={handleSearch} disabled={isLoading}>
                <Search className="w-4 h-4 mr-2" />
                {isLoading ? 'Searching...' : 'Search'}
              </Button>
            </div>
          </div>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="lg:col-span-1">
              <JobSearchFilters
                filters={filters}
                onFiltersChange={handleFilterChange}
                onApply={handleApplyFilters}
                onClose={() => setShowFilters(false)}
              />
            </div>
          )}

          {/* Job List */}
          <div className={showFilters ? 'lg:col-span-1' : 'lg:col-span-2'}>
            <JobList
              jobs={jobs}
              selectedJob={selectedJob}
              onJobSelect={setSelectedJob}
              isLoading={isLoading}
              savedJobs={savedJobs}
              onToggleSave={toggleSaveJob}
            />
          </div>

          {/* Job Details */}
          {selectedJob && (
            <div className="lg:col-span-1">
              <JobDetails
                job={selectedJob}
                isSaved={savedJobs.has(selectedJob.id)}
                onToggleSave={() => toggleSaveJob(selectedJob.id)}
                onClose={() => setSelectedJob(null)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
