'use client';

import { useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Loader2, ArrowRight } from 'lucide-react';

export default function SimpleSignInPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setStatus('idle');
    setMessage('');

    try {
      console.log('Starting sign in process...');
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password,
      });

      if (error) {
        console.error('Sign in error:', error);
        setStatus('error');
        setMessage(error.message);
        setIsLoading(false);
        return;
      }

      if (data.user) {
        console.log('Sign in successful:', data.user.email);
        setStatus('success');
        setMessage('Sign in successful! Redirecting...');
        
        // Simple, direct redirect without waiting for auth state changes
        setTimeout(() => {
          console.log('Redirecting to dashboard...');
          window.location.replace('/dashboard');
        }, 1000);
      }
    } catch (error: any) {
      console.error('Unexpected error:', error);
      setStatus('error');
      setMessage(`Unexpected error: ${error.message}`);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Simple Sign In</CardTitle>
          <p className="text-gray-600">Direct sign in without complex redirects</p>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSignIn} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                required
                disabled={isLoading}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                required
                disabled={isLoading}
              />
            </div>
            
            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Signing In...
                </>
              ) : (
                <>
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Sign In
                </>
              )}
            </Button>
          </form>

          {/* Status Messages */}
          {status === 'success' && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <p className="text-green-800 text-sm font-medium">{message}</p>
              </div>
            </div>
          )}

          {status === 'error' && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <XCircle className="w-4 h-4 text-red-500" />
                <p className="text-red-800 text-sm font-medium">{message}</p>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="mt-6 space-y-2">
            <Button 
              onClick={() => window.location.href = '/dashboard'} 
              variant="outline" 
              className="w-full"
              disabled={isLoading}
            >
              Go to Dashboard (Direct)
            </Button>
            
            <Button 
              onClick={() => window.location.href = '/auth-debug'} 
              variant="ghost" 
              className="w-full"
              disabled={isLoading}
            >
              Debug Auth Status
            </Button>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 mb-2">💡 How this works:</h4>
            <ul className="text-blue-700 text-xs space-y-1">
              <li>• Direct Supabase auth without complex redirects</li>
              <li>• Uses window.location.replace() for clean redirect</li>
              <li>• No auth provider interference</li>
              <li>• Simple and reliable</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
