'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import Link from 'next/link';

export default function AuthTestPage() {
  const { user, session, profile, loading } = useAuth();
  const [rawAuthData, setRawAuthData] = useState<any>(null);

  const checkRawAuth = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      setRawAuthData({
        session,
        user,
        sessionError: error,
        userError,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Auth check error:', error);
    }
  };

  useEffect(() => {
    checkRawAuth();
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    window.location.href = '/auth/signin';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Authentication Test</h1>
          <p className="text-gray-600">Check your current authentication status</p>
        </div>

        {/* Auth Provider Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {user ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )}
              Auth Provider Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong>Loading:</strong> {loading ? '⏳ Yes' : '✅ No'}
              </div>
              <div>
                <strong>Has User:</strong> {user ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Has Session:</strong> {session ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Has Profile:</strong> {profile ? '✅ Yes' : '❌ No'}
              </div>
            </div>

            {user && (
              <div className="space-y-2">
                <div><strong>Email:</strong> {user.email}</div>
                <div><strong>Email Confirmed:</strong> {user.email_confirmed_at ? '✅ Yes' : '❌ No'}</div>
                <div><strong>User ID:</strong> {user.id}</div>
                <div><strong>Created:</strong> {new Date(user.created_at!).toLocaleString()}</div>
              </div>
            )}

            {profile && (
              <div className="space-y-2">
                <div><strong>Profile Name:</strong> {profile.full_name || 'Not set'}</div>
                <div><strong>Profile Email:</strong> {profile.email}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Raw Auth Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Raw Supabase Auth Data
              <Button onClick={checkRawAuth} size="sm" variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {rawAuthData ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <strong>Raw Session:</strong> {rawAuthData.session ? '✅ Yes' : '❌ No'}
                  </div>
                  <div>
                    <strong>Raw User:</strong> {rawAuthData.user ? '✅ Yes' : '❌ No'}
                  </div>
                </div>
                
                {rawAuthData.user && (
                  <div className="space-y-2">
                    <div><strong>Raw Email:</strong> {rawAuthData.user.email}</div>
                    <div><strong>Raw Email Confirmed:</strong> {rawAuthData.user.email_confirmed_at ? '✅ Yes' : '❌ No'}</div>
                  </div>
                )}

                <details className="mt-4">
                  <summary className="cursor-pointer font-medium">View Raw Data</summary>
                  <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto mt-2">
                    {JSON.stringify(rawAuthData, null, 2)}
                  </pre>
                </details>
              </div>
            ) : (
              <p>Loading raw auth data...</p>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              {user ? (
                <>
                  <Button asChild>
                    <Link href="/dashboard">Go to Dashboard</Link>
                  </Button>
                  <Button onClick={signOut} variant="outline">
                    Sign Out
                  </Button>
                </>
              ) : (
                <>
                  <Button asChild>
                    <Link href="/auth/signin">Sign In</Link>
                  </Button>
                  <Button asChild variant="outline">
                    <Link href="/auth/signup">Sign Up</Link>
                  </Button>
                </>
              )}
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Expected Behavior</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• After successful sign in, you should be redirected to /dashboard</li>
                <li>• Auth Provider should show user data</li>
                <li>• Raw auth data should match provider data</li>
                <li>• Email should be confirmed for full access</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
