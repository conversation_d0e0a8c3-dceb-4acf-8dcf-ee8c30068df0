'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check<PERSON><PERSON>cle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

export default function TestConnectionPage() {
  const [connectionStatus, setConnectionStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  const testConnection = async () => {
    setIsLoading(true);
    const results: any = {
      timestamp: new Date().toISOString(),
      tests: []
    };

    try {
      // Test 1: Check environment variables
      results.tests.push({
        name: 'Environment Variables',
        status: process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'pass' : 'fail',
        details: {
          url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Missing',
          key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Missing',
          urlValue: process.env.NEXT_PUBLIC_SUPABASE_URL,
          keyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0
        }
      });

      // Test 2: Basic Supabase client initialization
      try {
        const client = supabase;
        results.tests.push({
          name: 'Supabase Client',
          status: 'pass',
          details: 'Client initialized successfully'
        });
      } catch (error: any) {
        results.tests.push({
          name: 'Supabase Client',
          status: 'fail',
          details: error.message
        });
      }

      // Test 3: Simple health check
      try {
        const { data, error } = await supabase
          .from('resume_templates')
          .select('count')
          .limit(1);
        
        results.tests.push({
          name: 'Database Connection',
          status: error ? 'fail' : 'pass',
          details: error ? error.message : 'Database accessible'
        });
      } catch (error: any) {
        results.tests.push({
          name: 'Database Connection',
          status: 'fail',
          details: error.message
        });
      }

      // Test 4: Auth status
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        results.tests.push({
          name: 'Auth Session',
          status: error ? 'fail' : 'pass',
          details: error ? error.message : session ? 'Session exists' : 'No session'
        });
      } catch (error: any) {
        results.tests.push({
          name: 'Auth Session',
          status: 'fail',
          details: error.message
        });
      }

      // Test 5: User data
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        
        results.tests.push({
          name: 'Current User',
          status: error ? 'fail' : 'pass',
          details: error ? error.message : user ? `User: ${user.email}` : 'No user'
        });
      } catch (error: any) {
        results.tests.push({
          name: 'Current User',
          status: 'fail',
          details: error.message
        });
      }

      // Test 6: Check if tables exist
      try {
        const { data, error } = await supabase
          .from('users')
          .select('count')
          .limit(1);
        
        results.tests.push({
          name: 'Users Table',
          status: error ? 'fail' : 'pass',
          details: error ? error.message : 'Users table accessible'
        });
      } catch (error: any) {
        results.tests.push({
          name: 'Users Table',
          status: 'fail',
          details: error.message
        });
      }

    } catch (error: any) {
      results.tests.push({
        name: 'General Error',
        status: 'fail',
        details: error.message
      });
    }

    setConnectionStatus(results);
    setIsLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'fail':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'text-green-700 bg-green-50 border-green-200';
      case 'fail':
        return 'text-red-700 bg-red-50 border-red-200';
      default:
        return 'text-yellow-700 bg-yellow-50 border-yellow-200';
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Connection Test</h1>
          <p className="text-gray-600">Testing Supabase connection and setup</p>
        </div>

        <div className="flex justify-center">
          <Button onClick={testConnection} disabled={isLoading}>
            {isLoading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            Run Tests
          </Button>
        </div>

        {connectionStatus && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <p className="text-sm text-gray-600">
                Tested at: {new Date(connectionStatus.timestamp).toLocaleString()}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {connectionStatus.tests.map((test: any, index: number) => (
                <div
                  key={index}
                  className={`p-4 border rounded-lg ${getStatusColor(test.status)}`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    {getStatusIcon(test.status)}
                    <h3 className="font-medium">{test.name}</h3>
                  </div>
                  <div className="text-sm">
                    {typeof test.details === 'string' ? (
                      <p>{test.details}</p>
                    ) : (
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(test.details, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {connectionStatus && (
          <Card>
            <CardHeader>
              <CardTitle>Environment Check</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Supabase URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set'}
                </div>
                <div>
                  <strong>Anon Key Length:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0} characters
                </div>
                <div>
                  <strong>App URL:</strong> {process.env.NEXT_PUBLIC_APP_URL || 'Not set'}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Quick Fixes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">If you see 400 errors:</h4>
              <ul className="text-sm space-y-1 ml-4">
                <li>• Check that your Supabase project is active</li>
                <li>• Verify environment variables are correct</li>
                <li>• Ensure database tables are created</li>
                <li>• Check Supabase project settings</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">If tables are missing:</h4>
              <ul className="text-sm space-y-1 ml-4">
                <li>• Run the SQL scripts in Supabase SQL Editor</li>
                <li>• Check Database → Tables in Supabase dashboard</li>
                <li>• Verify RLS policies are set up</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
