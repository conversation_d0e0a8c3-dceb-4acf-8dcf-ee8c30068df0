{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/types/database';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables');\n}\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n  },\n});\n"], "names": [], "mappings": ";;;AAGoB;AAHpB;;AAGA,MAAM;AACN,MAAM;AAEN,uCAAsC;;AAEtC;AAEO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAY,aAAa,iBAAiB;IAC3E,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;AACF", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/auth/auth-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase } from '@/lib/supabase/client';\nimport { Database } from '@/types/database';\n\ntype UserProfile = Database['public']['Tables']['users']['Row'];\n\ninterface AuthContextType {\n  user: User | null;\n  profile: UserProfile | null;\n  session: Session | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n  refreshProfile: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<UserProfile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        console.error('Error fetching profile:', error);\n        return null;\n      }\n\n      return data;\n    } catch (error) {\n      console.error('Unexpected error fetching profile:', error);\n      return null;\n    }\n  };\n\n  const refreshProfile = async () => {\n    if (user) {\n      const profileData = await fetchProfile(user.id);\n      setProfile(profileData);\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.error('Unexpected error during sign out:', error);\n    }\n  };\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession();\n        \n        if (error) {\n          console.error('Error getting session:', error);\n        } else {\n          setSession(session);\n          setUser(session?.user ?? null);\n          \n          if (session?.user) {\n            const profileData = await fetchProfile(session.user.id);\n            setProfile(profileData);\n          }\n        }\n      } catch (error) {\n        console.error('Unexpected error getting session:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    getInitialSession();\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session?.user?.id);\n        \n        setSession(session);\n        setUser(session?.user ?? null);\n        \n        if (session?.user) {\n          const profileData = await fetchProfile(session.user.id);\n          setProfile(profileData);\n\n          // If user just signed in and is on an auth page, redirect to dashboard\n          if (event === 'SIGNED_IN' && typeof window !== 'undefined') {\n            const currentPath = window.location.pathname;\n            if (currentPath.startsWith('/auth/') && !currentPath.includes('/verify-email')) {\n              setTimeout(() => {\n                window.location.href = '/dashboard';\n              }, 100);\n            }\n          }\n        } else {\n          setProfile(null);\n        }\n\n        setLoading(false);\n      }\n    );\n\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    profile,\n    session,\n    loading,\n    signOut,\n    refreshProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,cAAc,MAAM,aAAa,KAAK,EAAE;YAC9C,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;wBAEnE,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,0BAA0B;wBAC1C,OAAO;4BACL,WAAW;4BACX,QAAQ,SAAS,QAAQ;4BAEzB,IAAI,SAAS,MAAM;gCACjB,MAAM,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;gCACtD,WAAW;4BACb;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACrD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO,SAAS,MAAM;oBAEzD,WAAW;oBACX,QAAQ,SAAS,QAAQ;oBAEzB,IAAI,SAAS,MAAM;wBACjB,MAAM,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;wBACtD,WAAW;wBAEX,uEAAuE;wBACvE,IAAI,UAAU,eAAe,aAAkB,aAAa;4BAC1D,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;4BAC5C,IAAI,YAAY,UAAU,CAAC,aAAa,CAAC,YAAY,QAAQ,CAAC,kBAAkB;gCAC9E;8DAAW;wCACT,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACzB;6DAAG;4BACL;wBACF;oBACF,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAGF;0CAAO;oBACL,aAAa,WAAW;gBAC1B;;QACF;iCAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAtHgB;KAAA", "debugId": null}}]}