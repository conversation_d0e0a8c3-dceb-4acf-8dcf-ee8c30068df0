{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/types/database';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables');\n}\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n  },\n});\n"], "names": [], "mappings": ";;;AAGoB;AAHpB;;AAGA,MAAM;AACN,MAAM;AAEN,uCAAsC;;AAEtC;AAEO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAY,aAAa,iBAAiB;IAC3E,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;AACF", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/auth/simple-auth-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase } from '@/lib/supabase/client';\n\ninterface SimpleAuthContextType {\n  user: User | null;\n  session: Session | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n}\n\nconst SimpleAuthContext = createContext<SimpleAuthContextType>({\n  user: null,\n  session: null,\n  loading: true,\n  signOut: async () => {},\n});\n\nexport const useSimpleAuth = () => {\n  const context = useContext(SimpleAuthContext);\n  if (!context) {\n    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider');\n  }\n  return context;\n};\n\ninterface SimpleAuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function SimpleAuthProvider({ children }: SimpleAuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  const signOut = async () => {\n    try {\n      await supabase.auth.signOut();\n      setUser(null);\n      setSession(null);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  useEffect(() => {\n    let mounted = true;\n\n    const getInitialSession = async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession();\n        \n        if (mounted) {\n          if (error) {\n            console.error('Error getting session:', error);\n          } else {\n            setSession(session);\n            setUser(session?.user ?? null);\n          }\n          setLoading(false);\n        }\n      } catch (error) {\n        console.error('Unexpected error getting session:', error);\n        if (mounted) {\n          setLoading(false);\n        }\n      }\n    };\n\n    getInitialSession();\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (mounted) {\n          console.log('Auth state changed:', event);\n          setSession(session);\n          setUser(session?.user ?? null);\n          setLoading(false);\n\n          // Handle redirect after sign in (DISABLED to prevent conflicts)\n          // if (event === 'SIGNED_IN' && typeof window !== 'undefined') {\n          //   const currentPath = window.location.pathname;\n          //   console.log('SIGNED_IN event - current path:', currentPath);\n          //\n          //   if (currentPath.startsWith('/auth/') && !currentPath.includes('/verify-email')) {\n          //     console.log('Redirecting to dashboard after sign in...');\n          //     setTimeout(() => {\n          //       window.location.href = '/dashboard';\n          //     }, 500);\n          //   }\n          // }\n        }\n      }\n    );\n\n    return () => {\n      mounted = false;\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  const value = {\n    user,\n    session,\n    loading,\n    signOut,\n  };\n\n  return (\n    <SimpleAuthContext.Provider value={value}>\n      {children}\n    </SimpleAuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAaA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAyB;IAC7D,MAAM;IACN,SAAS;IACT,SAAS;IACT,SAAS,WAAa;AACxB;AAEO,MAAM,gBAAgB;;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,UAAU;QACd,IAAI;YACF,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3B,QAAQ;YACR,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,UAAU;YAEd,MAAM;kEAAoB;oBACxB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;wBAEnE,IAAI,SAAS;4BACX,IAAI,OAAO;gCACT,QAAQ,KAAK,CAAC,0BAA0B;4BAC1C,OAAO;gCACL,WAAW;gCACX,QAAQ,SAAS,QAAQ;4BAC3B;4BACA,WAAW;wBACb;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,qCAAqC;wBACnD,IAAI,SAAS;4BACX,WAAW;wBACb;oBACF;gBACF;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;gDAChE,OAAO,OAAO;oBACZ,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC,uBAAuB;wBACnC,WAAW;wBACX,QAAQ,SAAS,QAAQ;wBACzB,WAAW;oBAEX,gEAAgE;oBAChE,gEAAgE;oBAChE,kDAAkD;oBAClD,iEAAiE;oBACjE,EAAE;oBACF,sFAAsF;oBACtF,gEAAgE;oBAChE,yBAAyB;oBACzB,6CAA6C;oBAC7C,eAAe;oBACf,MAAM;oBACN,IAAI;oBACN;gBACF;;YAGF;gDAAO;oBACL,UAAU;oBACV,aAAa,WAAW;gBAC1B;;QACF;uCAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;IAnFgB;KAAA", "debugId": null}}]}