{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.slice(0, length) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  asChild?: boolean;\n  variant?:\n    | 'default'\n    | 'destructive'\n    | 'outline'\n    | 'secondary'\n    | 'ghost'\n    | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button';\n    return (\n      <Comp\n        className={cn(\n          'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n          {\n            'bg-blue-600 text-white hover:bg-blue-700':\n              variant === 'default',\n            'bg-red-600 text-white hover:bg-red-700':\n              variant === 'destructive',\n            'border border-gray-300 bg-white hover:bg-gray-50':\n              variant === 'outline',\n            'bg-gray-100 text-gray-900 hover:bg-gray-200':\n              variant === 'secondary',\n            'hover:bg-gray-100': variant === 'ghost',\n            'text-blue-600 underline-offset-4 hover:underline':\n              variant === 'link',\n          },\n          {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAeA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,4CACE,YAAY;YACd,0CACE,YAAY;YACd,oDACE,YAAY;YACd,+CACE,YAAY;YACd,qBAAqB,YAAY;YACjC,oDACE,YAAY;QAChB,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/app/system-check/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { supabase } from '@/lib/supabase/client';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { CheckCircle, XCircle, AlertCircle, RefreshCw, Database, Shield, Mail } from 'lucide-react';\n\ninterface CheckResult {\n  name: string;\n  status: 'pass' | 'fail' | 'warning';\n  message: string;\n  details?: any;\n  fix?: string;\n}\n\nexport default function SystemCheckPage() {\n  const [checks, setChecks] = useState<CheckResult[]>([]);\n  const [isRunning, setIsRunning] = useState(false);\n  const [summary, setSummary] = useState({ pass: 0, fail: 0, warning: 0 });\n\n  const runSystemCheck = async () => {\n    setIsRunning(true);\n    const results: CheckResult[] = [];\n\n    // 1. Environment Variables Check\n    try {\n      const hasUrl = !!process.env.NEXT_PUBLIC_SUPABASE_URL;\n      const hasKey = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n      const urlValid = process.env.NEXT_PUBLIC_SUPABASE_URL?.includes('supabase.co');\n      \n      if (hasUrl && hasKey && urlValid) {\n        results.push({\n          name: 'Environment Variables',\n          status: 'pass',\n          message: 'Supabase environment variables are configured correctly',\n          details: {\n            url: process.env.NEXT_PUBLIC_SUPABASE_URL,\n            keyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length\n          }\n        });\n      } else {\n        results.push({\n          name: 'Environment Variables',\n          status: 'fail',\n          message: 'Missing or invalid Supabase environment variables',\n          details: { hasUrl, hasKey, urlValid },\n          fix: 'Check your .env.local file and ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set correctly'\n        });\n      }\n    } catch (error) {\n      results.push({\n        name: 'Environment Variables',\n        status: 'fail',\n        message: 'Error checking environment variables',\n        details: error\n      });\n    }\n\n    // 2. Supabase Client Connection\n    try {\n      const client = supabase;\n      results.push({\n        name: 'Supabase Client',\n        status: 'pass',\n        message: 'Supabase client initialized successfully'\n      });\n    } catch (error: any) {\n      results.push({\n        name: 'Supabase Client',\n        status: 'fail',\n        message: 'Failed to initialize Supabase client',\n        details: error.message,\n        fix: 'Check your Supabase configuration and environment variables'\n      });\n    }\n\n    // 3. Database Connection Test\n    try {\n      const { data, error } = await supabase.from('users').select('count').limit(1);\n      \n      if (error) {\n        if (error.message.includes('relation \"public.users\" does not exist')) {\n          results.push({\n            name: 'Database Tables',\n            status: 'fail',\n            message: 'Database tables do not exist',\n            details: error.message,\n            fix: 'Run the database setup scripts (supabase-setup-safe.sql) in your Supabase SQL Editor'\n          });\n        } else {\n          results.push({\n            name: 'Database Connection',\n            status: 'fail',\n            message: 'Database connection failed',\n            details: error.message,\n            fix: 'Check your Supabase project status and database configuration'\n          });\n        }\n      } else {\n        results.push({\n          name: 'Database Connection',\n          status: 'pass',\n          message: 'Successfully connected to database'\n        });\n      }\n    } catch (error: any) {\n      results.push({\n        name: 'Database Connection',\n        status: 'fail',\n        message: 'Database connection error',\n        details: error.message\n      });\n    }\n\n    // 4. Check Required Tables\n    const requiredTables = ['users', 'resumes', 'resume_templates', 'jobs', 'job_applications'];\n    for (const table of requiredTables) {\n      try {\n        const { error } = await supabase.from(table).select('count').limit(1);\n        \n        if (error) {\n          results.push({\n            name: `Table: ${table}`,\n            status: 'fail',\n            message: `Table '${table}' does not exist or is not accessible`,\n            details: error.message,\n            fix: 'Run the database setup scripts in Supabase SQL Editor'\n          });\n        } else {\n          results.push({\n            name: `Table: ${table}`,\n            status: 'pass',\n            message: `Table '${table}' exists and is accessible`\n          });\n        }\n      } catch (error: any) {\n        results.push({\n          name: `Table: ${table}`,\n          status: 'fail',\n          message: `Error checking table '${table}'`,\n          details: error.message\n        });\n      }\n    }\n\n    // 5. Authentication Service Test\n    try {\n      const { data: { session }, error } = await supabase.auth.getSession();\n      \n      if (error) {\n        results.push({\n          name: 'Authentication Service',\n          status: 'fail',\n          message: 'Authentication service error',\n          details: error.message,\n          fix: 'Check Supabase authentication configuration'\n        });\n      } else {\n        results.push({\n          name: 'Authentication Service',\n          status: 'pass',\n          message: 'Authentication service is working',\n          details: { hasSession: !!session }\n        });\n      }\n    } catch (error: any) {\n      results.push({\n        name: 'Authentication Service',\n        status: 'fail',\n        message: 'Authentication service connection failed',\n        details: error.message\n      });\n    }\n\n    // 6. Check Auth Configuration\n    try {\n      // Try to get auth settings (this will fail if auth is misconfigured)\n      const { data: { user }, error } = await supabase.auth.getUser();\n      \n      if (error && !error.message.includes('session_not_found')) {\n        results.push({\n          name: 'Auth Configuration',\n          status: 'fail',\n          message: 'Authentication configuration issue',\n          details: error.message,\n          fix: 'Check Supabase authentication settings and site URL configuration'\n        });\n      } else {\n        results.push({\n          name: 'Auth Configuration',\n          status: 'pass',\n          message: 'Authentication configuration is valid',\n          details: { currentUser: user?.email || 'No user signed in' }\n        });\n      }\n    } catch (error: any) {\n      results.push({\n        name: 'Auth Configuration',\n        status: 'fail',\n        message: 'Auth configuration check failed',\n        details: error.message\n      });\n    }\n\n    // 7. RLS Policies Check\n    try {\n      // Try to access a protected table without authentication\n      const { error } = await supabase.from('users').select('*').limit(1);\n      \n      if (error && error.message.includes('RLS')) {\n        results.push({\n          name: 'Row Level Security',\n          status: 'pass',\n          message: 'RLS policies are active and protecting data'\n        });\n      } else if (error) {\n        results.push({\n          name: 'Row Level Security',\n          status: 'warning',\n          message: 'RLS policies may not be configured properly',\n          details: error.message,\n          fix: 'Run the RLS policies script (supabase-rls-policies.sql)'\n        });\n      } else {\n        results.push({\n          name: 'Row Level Security',\n          status: 'warning',\n          message: 'RLS policies may be too permissive or not configured',\n          fix: 'Review and apply RLS policies for data security'\n        });\n      }\n    } catch (error: any) {\n      results.push({\n        name: 'Row Level Security',\n        status: 'fail',\n        message: 'Error checking RLS policies',\n        details: error.message\n      });\n    }\n\n    // Calculate summary\n    const summary = results.reduce(\n      (acc, result) => {\n        acc[result.status]++;\n        return acc;\n      },\n      { pass: 0, fail: 0, warning: 0 }\n    );\n\n    setChecks(results);\n    setSummary(summary);\n    setIsRunning(false);\n  };\n\n  useEffect(() => {\n    runSystemCheck();\n  }, []);\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pass':\n        return <CheckCircle className=\"w-5 h-5 text-green-500\" />;\n      case 'fail':\n        return <XCircle className=\"w-5 h-5 text-red-500\" />;\n      case 'warning':\n        return <AlertCircle className=\"w-5 h-5 text-yellow-500\" />;\n      default:\n        return <AlertCircle className=\"w-5 h-5 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pass':\n        return 'border-green-200 bg-green-50';\n      case 'fail':\n        return 'border-red-200 bg-red-50';\n      case 'warning':\n        return 'border-yellow-200 bg-yellow-50';\n      default:\n        return 'border-gray-200 bg-gray-50';\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4\">\n      <div className=\"max-w-6xl mx-auto space-y-6\">\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold mb-2\">System Health Check</h1>\n          <p className=\"text-gray-600\">Comprehensive check of Supabase connection, database, and authentication</p>\n        </div>\n\n        {/* Summary */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <div className=\"text-2xl font-bold text-green-600\">{summary.pass}</div>\n              <div className=\"text-sm text-gray-600\">Passing</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <div className=\"text-2xl font-bold text-red-600\">{summary.fail}</div>\n              <div className=\"text-sm text-gray-600\">Failing</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <div className=\"text-2xl font-bold text-yellow-600\">{summary.warning}</div>\n              <div className=\"text-sm text-gray-600\">Warnings</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-4 text-center\">\n              <Button onClick={runSystemCheck} disabled={isRunning} className=\"w-full\">\n                {isRunning ? (\n                  <RefreshCw className=\"w-4 h-4 mr-2 animate-spin\" />\n                ) : (\n                  <RefreshCw className=\"w-4 h-4 mr-2\" />\n                )}\n                Re-run Check\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Detailed Results */}\n        <div className=\"space-y-4\">\n          {checks.map((check, index) => (\n            <Card key={index} className={getStatusColor(check.status)}>\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-start gap-3\">\n                  {getStatusIcon(check.status)}\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-medium mb-1\">{check.name}</h3>\n                    <p className=\"text-sm text-gray-700 mb-2\">{check.message}</p>\n                    \n                    {check.details && (\n                      <details className=\"mb-2\">\n                        <summary className=\"cursor-pointer text-xs text-gray-600\">View Details</summary>\n                        <pre className=\"bg-white p-2 rounded text-xs mt-1 overflow-auto\">\n                          {typeof check.details === 'string' ? check.details : JSON.stringify(check.details, null, 2)}\n                        </pre>\n                      </details>\n                    )}\n                    \n                    {check.fix && (\n                      <div className=\"bg-blue-50 border border-blue-200 rounded p-2 mt-2\">\n                        <p className=\"text-xs text-blue-800\">\n                          <strong>Fix:</strong> {check.fix}\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Quick Actions */}\n        {summary.fail > 0 && (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <AlertCircle className=\"w-5 h-5 text-red-500\" />\n                Critical Issues Found\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n                <h4 className=\"font-medium text-red-800 mb-2\">Immediate Actions Required:</h4>\n                <ul className=\"text-sm text-red-700 space-y-1\">\n                  <li>• If database tables are missing: Run database setup scripts in Supabase SQL Editor</li>\n                  <li>• If environment variables are wrong: Check your .env.local file</li>\n                  <li>• If Supabase connection fails: Verify your project is active and credentials are correct</li>\n                  <li>• If authentication fails: Check Supabase auth settings and site URL</li>\n                </ul>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AA2BuB;;AAzBvB;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAG,MAAM;QAAG,SAAS;IAAE;IAEtE,MAAM,iBAAiB;QACrB,aAAa;QACb,MAAM,UAAyB,EAAE;QAEjC,iCAAiC;QACjC,IAAI;YACF,MAAM,SAAS,CAAC;YAChB,MAAM,SAAS,CAAC;YAChB,MAAM,yFAAiD,SAAS;YAEhE,IAAI,UAAU,UAAU,UAAU;gBAChC,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,SAAS;wBACP,GAAG;wBACH,iQAAsD;oBACxD;gBACF;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,SAAS;wBAAE;wBAAQ;wBAAQ;oBAAS;oBACpC,KAAK;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS;YACX;QACF;QAEA,gCAAgC;QAChC,IAAI;YACF,MAAM,SAAS,mIAAA,CAAA,WAAQ;YACvB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;YACX;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS,MAAM,OAAO;gBACtB,KAAK;YACP;QACF;QAEA,8BAA8B;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,SAAS,KAAK,CAAC;YAE3E,IAAI,OAAO;gBACT,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,2CAA2C;oBACpE,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,QAAQ;wBACR,SAAS;wBACT,SAAS,MAAM,OAAO;wBACtB,KAAK;oBACP;gBACF,OAAO;oBACL,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,QAAQ;wBACR,SAAS;wBACT,SAAS,MAAM,OAAO;wBACtB,KAAK;oBACP;gBACF;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS,MAAM,OAAO;YACxB;QACF;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB;YAAC;YAAS;YAAW;YAAoB;YAAQ;SAAmB;QAC3F,KAAK,MAAM,SAAS,eAAgB;YAClC,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,SAAS,KAAK,CAAC;gBAEnE,IAAI,OAAO;oBACT,QAAQ,IAAI,CAAC;wBACX,MAAM,CAAC,OAAO,EAAE,OAAO;wBACvB,QAAQ;wBACR,SAAS,CAAC,OAAO,EAAE,MAAM,qCAAqC,CAAC;wBAC/D,SAAS,MAAM,OAAO;wBACtB,KAAK;oBACP;gBACF,OAAO;oBACL,QAAQ,IAAI,CAAC;wBACX,MAAM,CAAC,OAAO,EAAE,OAAO;wBACvB,QAAQ;wBACR,SAAS,CAAC,OAAO,EAAE,MAAM,0BAA0B,CAAC;oBACtD;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,IAAI,CAAC;oBACX,MAAM,CAAC,OAAO,EAAE,OAAO;oBACvB,QAAQ;oBACR,SAAS,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;oBAC1C,SAAS,MAAM,OAAO;gBACxB;YACF;QACF;QAEA,iCAAiC;QACjC,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAEnE,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,SAAS,MAAM,OAAO;oBACtB,KAAK;gBACP;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,SAAS;wBAAE,YAAY,CAAC,CAAC;oBAAQ;gBACnC;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS,MAAM,OAAO;YACxB;QACF;QAEA,8BAA8B;QAC9B,IAAI;YACF,qEAAqE;YACrE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAE7D,IAAI,SAAS,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB;gBACzD,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,SAAS,MAAM,OAAO;oBACtB,KAAK;gBACP;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,SAAS;wBAAE,aAAa,MAAM,SAAS;oBAAoB;gBAC7D;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS,MAAM,OAAO;YACxB;QACF;QAEA,wBAAwB;QACxB,IAAI;YACF,yDAAyD;YACzD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,KAAK,CAAC;YAEjE,IAAI,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBAC1C,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;gBACX;YACF,OAAO,IAAI,OAAO;gBAChB,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,SAAS,MAAM,OAAO;oBACtB,KAAK;gBACP;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;oBACX,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,KAAK;gBACP;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS,MAAM,OAAO;YACxB;QACF;QAEA,oBAAoB;QACpB,MAAM,UAAU,QAAQ,MAAM,CAC5B,CAAC,KAAK;YACJ,GAAG,CAAC,OAAO,MAAM,CAAC;YAClB,OAAO;QACT,GACA;YAAE,MAAM;YAAG,MAAM;YAAG,SAAS;QAAE;QAGjC,UAAU;QACV,WAAW;QACX,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAqC,QAAQ,IAAI;;;;;;kDAChE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAG3C,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAmC,QAAQ,IAAI;;;;;;kDAC9D,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAG3C,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAsC,QAAQ,OAAO;;;;;;kDACpE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAG3C,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAgB,UAAU;oCAAW,WAAU;;wCAC7D,0BACC,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCACrB;;;;;;;;;;;;;;;;;;;;;;;8BAQV,6LAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,mIAAA,CAAA,OAAI;4BAAa,WAAW,eAAe,MAAM,MAAM;sCACtD,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,cAAc,MAAM,MAAM;sDAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB,MAAM,IAAI;;;;;;8DAC5C,6LAAC;oDAAE,WAAU;8DAA8B,MAAM,OAAO;;;;;;gDAEvD,MAAM,OAAO,kBACZ,6LAAC;oDAAQ,WAAU;;sEACjB,6LAAC;4DAAQ,WAAU;sEAAuC;;;;;;sEAC1D,6LAAC;4DAAI,WAAU;sEACZ,OAAO,MAAM,OAAO,KAAK,WAAW,MAAM,OAAO,GAAG,KAAK,SAAS,CAAC,MAAM,OAAO,EAAE,MAAM;;;;;;;;;;;;gDAK9F,MAAM,GAAG,kBACR,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;0EAAO;;;;;;4DAAa;4DAAE,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BApBnC;;;;;;;;;;gBAgCd,QAAQ,IAAI,GAAG,mBACd,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;;;;;;sCAIpD,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;GAlXwB;KAAA", "debugId": null}}]}