{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.slice(0, length) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  asChild?: boolean;\n  variant?:\n    | 'default'\n    | 'destructive'\n    | 'outline'\n    | 'secondary'\n    | 'ghost'\n    | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button';\n    return (\n      <Comp\n        className={cn(\n          'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n          {\n            'bg-blue-600 text-white hover:bg-blue-700':\n              variant === 'default',\n            'bg-red-600 text-white hover:bg-red-700':\n              variant === 'destructive',\n            'border border-gray-300 bg-white hover:bg-gray-50':\n              variant === 'outline',\n            'bg-gray-100 text-gray-900 hover:bg-gray-200':\n              variant === 'secondary',\n            'hover:bg-gray-100': variant === 'ghost',\n            'text-blue-600 underline-offset-4 hover:underline':\n              variant === 'link',\n          },\n          {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAeA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,4CACE,YAAY;YACd,0CACE,YAAY;YACd,oDACE,YAAY;YACd,+CACE,YAAY;YACd,qBAAqB,YAAY;YACjC,oDACE,YAAY;QAChB,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/app/hydration-test/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { CheckCircle, XCircle, Loader2 } from 'lucide-react';\n\nexport default function HydrationTestPage() {\n  const [mounted, setMounted] = useState(false);\n  const [currentUrl, setCurrentUrl] = useState('');\n  const [currentPath, setCurrentPath] = useState('');\n\n  useEffect(() => {\n    setMounted(true);\n    setCurrentUrl(window.location.href);\n    setCurrentPath(window.location.pathname);\n  }, []);\n\n  // Prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"w-8 h-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Loading page...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4\">\n      <div className=\"max-w-2xl mx-auto space-y-6\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-2\">Hydration Test</h1>\n          <p className=\"text-gray-600\">Testing hydration-safe rendering</p>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <CheckCircle className=\"w-5 h-5 text-green-500\" />\n              Hydration Status\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\n                <p className=\"text-green-800 text-sm font-medium\">\n                  ✅ Page mounted successfully without hydration errors!\n                </p>\n              </div>\n\n              <div className=\"space-y-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span>Mounted:</span>\n                  <span className=\"text-green-600\">✅ Yes</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Current URL:</span>\n                  <span className=\"font-mono text-xs break-all\">{currentUrl}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Current Path:</span>\n                  <span className=\"font-mono text-xs\">{currentPath}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Timestamp:</span>\n                  <span className=\"text-xs\">{new Date().toLocaleString()}</span>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Navigation Test</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <p className=\"text-sm text-gray-600 mb-4\">\n              These buttons should work without hydration issues:\n            </p>\n            \n            <div className=\"grid grid-cols-2 gap-3\">\n              <Button \n                onClick={() => window.location.href = '/'} \n                variant=\"outline\"\n              >\n                Home\n              </Button>\n              <Button \n                onClick={() => window.location.href = '/auth-status'} \n                variant=\"outline\"\n              >\n                Auth Status\n              </Button>\n              <Button \n                onClick={() => window.location.href = '/quick-signin'} \n                variant=\"outline\"\n              >\n                Quick Sign In\n              </Button>\n              <Button \n                onClick={() => window.location.href = '/dashboard'} \n                variant=\"outline\"\n              >\n                Dashboard\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Hydration Best Practices</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3 text-sm\">\n              <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n                <h4 className=\"font-medium text-blue-800 mb-2\">✅ What We Fixed:</h4>\n                <ul className=\"text-blue-700 space-y-1\">\n                  <li>• Added `mounted` state to prevent SSR/client mismatch</li>\n                  <li>• Used `useEffect` to safely access `window` object</li>\n                  <li>• Added loading state during hydration</li>\n                  <li>• Prevented rendering until client-side mount</li>\n                </ul>\n              </div>\n\n              <div className=\"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                <h4 className=\"font-medium text-yellow-800 mb-2\">⚠️ Common Hydration Issues:</h4>\n                <ul className=\"text-yellow-700 space-y-1\">\n                  <li>• Accessing `window` during server-side rendering</li>\n                  <li>• Different content between server and client</li>\n                  <li>• Date/time differences between server and client</li>\n                  <li>• Random values that differ between renders</li>\n                </ul>\n              </div>\n\n              <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\n                <h4 className=\"font-medium text-green-800 mb-2\">✅ Solution Pattern:</h4>\n                <pre className=\"text-green-700 text-xs bg-white p-2 rounded mt-2 overflow-auto\">\n{`const [mounted, setMounted] = useState(false);\n\nuseEffect(() => {\n  setMounted(true);\n}, []);\n\nif (!mounted) {\n  return <LoadingComponent />;\n}\n\n// Safe to use window object here`}\n                </pre>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,WAAW;YACX,cAAc,OAAO,QAAQ,CAAC,IAAI;YAClC,eAAe,OAAO,QAAQ,CAAC,QAAQ;QACzC;sCAAG,EAAE;IAEL,6BAA6B;IAC7B,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;;;;;;sCAItD,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAAiB;;;;;;;;;;;;0DAEnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAAW,IAAI,OAAO,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9D,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACtC,SAAQ;sDACT;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACtC,SAAQ;sDACT;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACtC,SAAQ;sDACT;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACtC,SAAQ;sDACT;;;;;;;;;;;;;;;;;;;;;;;;8BAOP,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAC9B,CAAC;;;;;;;;;;iCAU+B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;GA1JwB;KAAA", "debugId": null}}]}