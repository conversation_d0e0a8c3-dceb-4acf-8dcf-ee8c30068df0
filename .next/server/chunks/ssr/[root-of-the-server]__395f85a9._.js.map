{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/types/database';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables');\n}\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n  },\n});\n"], "names": [], "mappings": ";;;AAAA;;AAGA,MAAM;AACN,MAAM;AAEN,uCAAsC;;AAEtC;AAEO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAY,aAAa,iBAAiB;IAC3E,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;AACF", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/auth/auth-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { supabase } from '@/lib/supabase/client';\nimport { Database } from '@/types/database';\n\ntype UserProfile = Database['public']['Tables']['users']['Row'];\n\ninterface AuthContextType {\n  user: User | null;\n  profile: UserProfile | null;\n  session: Session | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n  refreshProfile: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null);\n  const [profile, setProfile] = useState<UserProfile | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        console.error('Error fetching profile:', error);\n        return null;\n      }\n\n      return data;\n    } catch (error) {\n      console.error('Unexpected error fetching profile:', error);\n      return null;\n    }\n  };\n\n  const refreshProfile = async () => {\n    if (user) {\n      const profileData = await fetchProfile(user.id);\n      setProfile(profileData);\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) {\n        console.error('Error signing out:', error);\n      }\n    } catch (error) {\n      console.error('Unexpected error during sign out:', error);\n    }\n  };\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession();\n        \n        if (error) {\n          console.error('Error getting session:', error);\n        } else {\n          setSession(session);\n          setUser(session?.user ?? null);\n          \n          if (session?.user) {\n            const profileData = await fetchProfile(session.user.id);\n            setProfile(profileData);\n          }\n        }\n      } catch (error) {\n        console.error('Unexpected error getting session:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    getInitialSession();\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session?.user?.id);\n        \n        setSession(session);\n        setUser(session?.user ?? null);\n        \n        if (session?.user) {\n          const profileData = await fetchProfile(session.user.id);\n          setProfile(profileData);\n\n          // If user just signed in and is on an auth page, redirect to dashboard\n          if (event === 'SIGNED_IN' && typeof window !== 'undefined') {\n            const currentPath = window.location.pathname;\n            if (currentPath.startsWith('/auth/') && !currentPath.includes('/verify-email')) {\n              setTimeout(() => {\n                window.location.href = '/dashboard';\n              }, 100);\n            }\n          }\n        } else {\n          setProfile(null);\n        }\n\n        setLoading(false);\n      }\n    );\n\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    profile,\n    session,\n    loading,\n    signOut,\n    refreshProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,cAAc,MAAM,aAAa,KAAK,EAAE;YAC9C,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAEnE,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C,OAAO;oBACL,WAAW;oBACX,QAAQ,SAAS,QAAQ;oBAEzB,IAAI,SAAS,MAAM;wBACjB,MAAM,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;wBACtD,WAAW;oBACb;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO,SAAS,MAAM;YAEzD,WAAW;YACX,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;gBACtD,WAAW;gBAEX,uEAAuE;gBACvE,uCAA4D;;gBAO5D;YACF,OAAO;gBACL,WAAW;YACb;YAEA,WAAW;QACb;QAGF,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}]}