{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.slice(0, length) + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  asChild?: boolean;\n  variant?:\n    | 'default'\n    | 'destructive'\n    | 'outline'\n    | 'secondary'\n    | 'ghost'\n    | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button';\n    return (\n      <Comp\n        className={cn(\n          'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n          {\n            'bg-blue-600 text-white hover:bg-blue-700':\n              variant === 'default',\n            'bg-red-600 text-white hover:bg-red-700':\n              variant === 'destructive',\n            'border border-gray-300 bg-white hover:bg-gray-50':\n              variant === 'outline',\n            'bg-gray-100 text-gray-900 hover:bg-gray-200':\n              variant === 'secondary',\n            'hover:bg-gray-100': variant === 'ghost',\n            'text-blue-600 underline-offset-4 hover:underline':\n              variant === 'link',\n          },\n          {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAeA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAChF,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,4CACE,YAAY;YACd,0CACE,YAAY;YACd,oDACE,YAAY;YACd,+CACE,YAAY;YACd,qBAAqB,YAAY;YACjC,oDACE,YAAY;QAChB,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',\n        className\n      )}\n      {...props}\n    />\n  )\n);\nLabel.displayName = 'Label';\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cvleaf/src/app/auth-debug/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useSimpleAuth } from '@/components/auth/simple-auth-provider';\nimport { supabase } from '@/lib/supabase/client';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { CheckCircle, XCircle, Loader2, ArrowRight, RefreshCw } from 'lucide-react';\n\nexport default function AuthDebugPage() {\n  const { user, session, loading } = useSimpleAuth();\n  const [mounted, setMounted] = useState(false);\n  const [authLogs, setAuthLogs] = useState<string[]>([]);\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [isSigningIn, setIsSigningIn] = useState(false);\n  const [rawAuthData, setRawAuthData] = useState<any>(null);\n\n  const addLog = (message: string) => {\n    const timestamp = new Date().toLocaleTimeString();\n    setAuthLogs(prev => [...prev, `[${timestamp}] ${message}`]);\n    console.log(`[DEBUG AUTH] ${message}`);\n  };\n\n  useEffect(() => {\n    setMounted(true);\n    addLog('Component mounted');\n    \n    // Check initial auth state\n    checkAuthState();\n    \n    // Listen to auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {\n      addLog(`Auth state change: ${event} - User: ${session?.user?.email || 'None'}`);\n      checkAuthState();\n    });\n\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  const checkAuthState = async () => {\n    try {\n      const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n      const { data: { user }, error: userError } = await supabase.auth.getUser();\n      \n      setRawAuthData({\n        session,\n        user,\n        sessionError,\n        userError,\n        timestamp: new Date().toISOString()\n      });\n\n      addLog(`Session check - Session: ${session ? 'EXISTS' : 'NULL'}, User: ${user ? user.email : 'NULL'}`);\n      \n      if (sessionError) addLog(`Session Error: ${sessionError.message}`);\n      if (userError) addLog(`User Error: ${userError.message}`);\n      \n    } catch (error: any) {\n      addLog(`Auth check failed: ${error.message}`);\n    }\n  };\n\n  const handleSignIn = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSigningIn(true);\n    addLog(`Starting sign in for: ${email}`);\n\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email: email.trim(),\n        password: password,\n      });\n\n      if (error) {\n        addLog(`Sign in error: ${error.message}`);\n        setIsSigningIn(false);\n        return;\n      }\n\n      if (data.user) {\n        addLog(`Sign in successful! User: ${data.user.email}`);\n        addLog(`Session: ${data.session ? 'Created' : 'Not created'}`);\n        \n        // Check auth state after sign in\n        setTimeout(() => {\n          checkAuthState();\n          addLog('Checking auth state after sign in...');\n        }, 1000);\n        \n        // Try manual redirect after a delay\n        setTimeout(() => {\n          addLog('Attempting manual redirect to dashboard...');\n          window.location.href = '/dashboard';\n        }, 2000);\n      }\n    } catch (error: any) {\n      addLog(`Unexpected error: ${error.message}`);\n    } finally {\n      setIsSigningIn(false);\n    }\n  };\n\n  const clearLogs = () => {\n    setAuthLogs([]);\n  };\n\n  const testDirectDashboard = () => {\n    addLog('Testing direct dashboard access...');\n    window.location.href = '/dashboard';\n  };\n\n  const signOut = async () => {\n    addLog('Signing out...');\n    await supabase.auth.signOut();\n    addLog('Sign out complete');\n  };\n\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"w-8 h-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Loading debug page...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4\">\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-2\">🔍 Authentication Debug Center</h1>\n          <p className=\"text-gray-600\">Comprehensive auth state debugging and testing</p>\n        </div>\n\n        {/* Current Auth Status */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              {loading ? (\n                <Loader2 className=\"w-5 h-5 animate-spin text-blue-500\" />\n              ) : user ? (\n                <CheckCircle className=\"w-5 h-5 text-green-500\" />\n              ) : (\n                <XCircle className=\"w-5 h-5 text-red-500\" />\n              )}\n              Current Auth Status\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span>Provider Loading:</span>\n                  <span className={loading ? 'text-yellow-600' : 'text-green-600'}>\n                    {loading ? '⏳ Yes' : '✅ No'}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Provider User:</span>\n                  <span className={user ? 'text-green-600' : 'text-red-600'}>\n                    {user ? '✅ Yes' : '❌ No'}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Provider Session:</span>\n                  <span className={session ? 'text-green-600' : 'text-red-600'}>\n                    {session ? '✅ Yes' : '❌ No'}\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span>Raw User:</span>\n                  <span className={rawAuthData?.user ? 'text-green-600' : 'text-red-600'}>\n                    {rawAuthData?.user ? '✅ Yes' : '❌ No'}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Raw Session:</span>\n                  <span className={rawAuthData?.session ? 'text-green-600' : 'text-red-600'}>\n                    {rawAuthData?.session ? '✅ Yes' : '❌ No'}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Mounted:</span>\n                  <span className=\"text-green-600\">✅ Yes</span>\n                </div>\n              </div>\n            </div>\n\n            {user && (\n              <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\">\n                <h4 className=\"font-medium text-green-800 mb-2\">✅ User Details:</h4>\n                <div className=\"text-sm text-green-700 space-y-1\">\n                  <div>Email: {user.email}</div>\n                  <div>ID: {user.id}</div>\n                  <div>Email Confirmed: {user.email_confirmed_at ? '✅ Yes' : '❌ No'}</div>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Sign In Test */}\n        <Card>\n          <CardHeader>\n            <CardTitle>🔐 Sign In Test</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSignIn} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"email\">Email</Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    placeholder=\"Enter your email\"\n                    required\n                    disabled={isSigningIn}\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"password\">Password</Label>\n                  <Input\n                    id=\"password\"\n                    type=\"password\"\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                    placeholder=\"Enter your password\"\n                    required\n                    disabled={isSigningIn}\n                  />\n                </div>\n              </div>\n              \n              <div className=\"flex gap-3\">\n                <Button type=\"submit\" disabled={isSigningIn} className=\"flex-1\">\n                  {isSigningIn ? (\n                    <>\n                      <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                      Signing In...\n                    </>\n                  ) : (\n                    <>\n                      <ArrowRight className=\"w-4 h-4 mr-2\" />\n                      Debug Sign In\n                    </>\n                  )}\n                </Button>\n                \n                <Button type=\"button\" onClick={checkAuthState} variant=\"outline\">\n                  <RefreshCw className=\"w-4 h-4 mr-2\" />\n                  Check Auth\n                </Button>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n\n        {/* Actions */}\n        <Card>\n          <CardHeader>\n            <CardTitle>🚀 Actions</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <div className=\"grid grid-cols-2 gap-3\">\n              <Button onClick={testDirectDashboard} variant=\"outline\">\n                Test Dashboard Access\n              </Button>\n              <Button onClick={() => window.location.href = '/auth-status'} variant=\"outline\">\n                Go to Auth Status\n              </Button>\n              <Button onClick={() => window.location.href = '/'} variant=\"outline\">\n                Go to Home\n              </Button>\n              <Button onClick={signOut} variant=\"destructive\">\n                Sign Out\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Debug Logs */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center justify-between\">\n              📝 Debug Logs\n              <Button onClick={clearLogs} size=\"sm\" variant=\"outline\">\n                Clear Logs\n              </Button>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto\">\n              {authLogs.length === 0 ? (\n                <div className=\"text-gray-500\">No logs yet...</div>\n              ) : (\n                authLogs.map((log, index) => (\n                  <div key={index} className=\"mb-1\">{log}</div>\n                ))\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEpD,MAAM,SAAS,CAAC;QACd,MAAM,YAAY,IAAI,OAAO,kBAAkB;QAC/C,YAAY,CAAA,OAAQ;mBAAI;gBAAM,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,SAAS;aAAC;QAC1D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS;IACvC;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO;QAEP,2BAA2B;QAC3B;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO;YACzE,OAAO,CAAC,mBAAmB,EAAE,MAAM,SAAS,EAAE,SAAS,MAAM,SAAS,QAAQ;YAC9E;QACF;QAEA,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YACjF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAExE,eAAe;gBACb;gBACA;gBACA;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,OAAO,CAAC,yBAAyB,EAAE,UAAU,WAAW,OAAO,QAAQ,EAAE,OAAO,KAAK,KAAK,GAAG,QAAQ;YAErG,IAAI,cAAc,OAAO,CAAC,eAAe,EAAE,aAAa,OAAO,EAAE;YACjE,IAAI,WAAW,OAAO,CAAC,YAAY,EAAE,UAAU,OAAO,EAAE;QAE1D,EAAE,OAAO,OAAY;YACnB,OAAO,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;QAC9C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,eAAe;QACf,OAAO,CAAC,sBAAsB,EAAE,OAAO;QAEvC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D,OAAO,MAAM,IAAI;gBACjB,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,OAAO,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;gBACxC,eAAe;gBACf;YACF;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,OAAO,CAAC,0BAA0B,EAAE,KAAK,IAAI,CAAC,KAAK,EAAE;gBACrD,OAAO,CAAC,SAAS,EAAE,KAAK,OAAO,GAAG,YAAY,eAAe;gBAE7D,iCAAiC;gBACjC,WAAW;oBACT;oBACA,OAAO;gBACT,GAAG;gBAEH,oCAAoC;gBACpC,WAAW;oBACT,OAAO;oBACP,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB,GAAG;YACL;QACF,EAAE,OAAO,OAAY;YACnB,OAAO,CAAC,kBAAkB,EAAE,MAAM,OAAO,EAAE;QAC7C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,YAAY;QAChB,YAAY,EAAE;IAChB;IAEA,MAAM,sBAAsB;QAC1B,OAAO;QACP,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,UAAU;QACd,OAAO;QACP,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC3B,OAAO;IACT;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;oCAClB,wBACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;+CACjB,qBACF,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCACnB;;;;;;;;;;;;sCAIN,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAW,UAAU,oBAAoB;sEAC5C,UAAU,UAAU;;;;;;;;;;;;8DAGzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAW,OAAO,mBAAmB;sEACxC,OAAO,UAAU;;;;;;;;;;;;8DAGtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAW,UAAU,mBAAmB;sEAC3C,UAAU,UAAU;;;;;;;;;;;;;;;;;;sDAK3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAW,aAAa,OAAO,mBAAmB;sEACrD,aAAa,OAAO,UAAU;;;;;;;;;;;;8DAGnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAW,aAAa,UAAU,mBAAmB;sEACxD,aAAa,UAAU,UAAU;;;;;;;;;;;;8DAGtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;gCAKtC,sBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAI;wDAAQ,KAAK,KAAK;;;;;;;8DACvB,8OAAC;;wDAAI;wDAAK,KAAK,EAAE;;;;;;;8DACjB,8OAAC;;wDAAI;wDAAkB,KAAK,kBAAkB,GAAG,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrE,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,aAAY;wDACZ,QAAQ;wDACR,UAAU;;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,aAAY;wDACZ,QAAQ;wDACR,UAAU;;;;;;;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,UAAU;gDAAa,WAAU;0DACpD,4BACC;;sEACE,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAA8B;;iFAInD;;sEACE,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;0DAM7C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAS;gDAAgB,SAAQ;;kEACrD,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAShD,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAqB,SAAQ;kDAAU;;;;;;kDAGxD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCAAgB,SAAQ;kDAAU;;;;;;kDAGhF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCAAK,SAAQ;kDAAU;;;;;;kDAGrE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAS,SAAQ;kDAAc;;;;;;;;;;;;;;;;;;;;;;;8BAQtD,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;oCAAoC;kDAEvD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAW,MAAK;wCAAK,SAAQ;kDAAU;;;;;;;;;;;;;;;;;sCAK5D,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;oCAAI,WAAU;8CAAgB;;;;;2CAE/B,SAAS,GAAG,CAAC,CAAC,KAAK,sBACjB,8OAAC;wCAAgB,WAAU;kDAAQ;uCAAzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B", "debugId": null}}]}