{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ldNwDaW7PP1415liviacWXE9vKp/UmphVXrfJafsDqU=", "__NEXT_PREVIEW_MODE_ID": "cc10e84c149fd983fb9ca7edbc512e94", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "30dcd1bce93e920154547641dc84586f6a7183d8e6f255a02db37ce71b7a604d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bb9973c09f4da7e9b473545abe47e0b643caaea09caf3e36167fc9feca5eb6d9"}}}, "sortedMiddleware": ["/"], "functions": {}}