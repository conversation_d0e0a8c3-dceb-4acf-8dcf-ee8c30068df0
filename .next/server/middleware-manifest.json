{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ldNwDaW7PP1415liviacWXE9vKp/UmphVXrfJafsDqU=", "__NEXT_PREVIEW_MODE_ID": "52b8772787f921bfd2f529d7342447c9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b3fbfdd6d3222f4321d27067fe106ed422ba1779e05d96dcf5058ff25d5cfc1a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "63bc77f6d1b8c4b35b9d2d7f72c024bef25ad447f172f161b7961b61ce1e79cd"}}}, "sortedMiddleware": ["/"], "functions": {}}