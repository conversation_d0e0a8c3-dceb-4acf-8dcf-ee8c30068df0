{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ldNwDaW7PP1415liviacWXE9vKp/UmphVXrfJafsDqU=", "__NEXT_PREVIEW_MODE_ID": "a510cf8235fd92ab6abcb297eee7f8e5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1f8c79eb07c601b874c54186bd4ac506eb5cefa837fa9d67c39819c7a96ecc75", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dd7d7f6dec2468d9ec7fb61177b9f6dca41c183911a6ac90fb8ca91bb551d2aa"}}}, "sortedMiddleware": ["/"], "functions": {}}